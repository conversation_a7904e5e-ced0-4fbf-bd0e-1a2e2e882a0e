const {
    <PERSON>lashCommandSubcommand<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');

// Keep track of in-flight games
const activeGames = new Map();

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('rps')
        .setDescription('Play Rock Paper Scissors')
        .addUserOption(opt =>
            opt.setName('opponent')
                .setDescription('User to challenge')
                .setRequired(true)
        ),

    async execute(interaction) {
        const challenger = interaction.user;
        const opponent = interaction.options.getUser('opponent');

        if (challenger.id === opponent.id) {
            return interaction.reply({ 
                content: '❌ You cannot play against yourself!', 
                ephemeral: true 
            });
        }
        
        if (opponent.bot) {
            return interaction.reply({ 
                content: '❌ You cannot play against a bot!', 
                ephemeral: true 
            });
        }

        // Unique game key (sorted to avoid duplicates)
        const gameId = [challenger.id, opponent.id].sort().join('_');
        if (activeGames.has(gameId)) {
            return interaction.reply({ 
                content: '❌ One of you is already in an active game!', 
                ephemeral: true 
            });
        }

        activeGames.set(gameId, { 
            challenger: challenger.id, 
            opponent: opponent.id, 
            moves: {},
            startTime: Date.now()
        });

        const embed = new EmbedBuilder()
            .setTitle('🎮 Rock Paper Scissors')
            .setDescription(`${challenger} has challenged ${opponent}!`)
            .setColor('#ff6b6b')
            .addFields(
                { name: '🔥 Challenger', value: challenger.displayName, inline: true },
                { name: '⚔️ Opponent', value: opponent.displayName, inline: true },
                { name: '📊 Status', value: 'Waiting for moves...', inline: true }
            )
            .setFooter({ text: 'Expires in 2 minutes' })
            .setTimestamp();

        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId(`rps_rock_${gameId}`)
                .setLabel('🪨 Rock')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId(`rps_paper_${gameId}`)
                .setLabel('📄 Paper')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId(`rps_scissors_${gameId}`)
                .setLabel('✂️ Scissors')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId(`rps_cancel_${gameId}`)
                .setLabel('❌ Cancel')
                .setStyle(ButtonStyle.Danger)
        );

        await interaction.reply({ 
            content: `${challenger} vs ${opponent}`, 
            embeds: [embed], 
            components: [row] 
        });

        // Auto-expire after 2 minutes
        setTimeout(() => {
            if (activeGames.has(gameId)) {
                activeGames.delete(gameId);
                console.log(`RPS game ${gameId} expired`);
            }
        }, 120_000);
    },

    async handleButtonInteraction(interaction) {
        const parts = interaction.customId.split('_');
        const action = parts[1];
        const gameId = parts.slice(2).join('_');
        
        const game = activeGames.get(gameId);
        if (!game) {
            return interaction.reply({ 
                content: '❌ This game has expired or does not exist!', 
                ephemeral: true 
            });
        }

        const userId = interaction.user.id;
        const { challenger, opponent } = game;

        if (![challenger, opponent].includes(userId)) {
            return interaction.reply({ 
                content: '❌ You are not part of this game!', 
                ephemeral: true 
            });
        }

        // Handle cancel
        if (action === 'cancel') {
            activeGames.delete(gameId);
            const cancelEmbed = new EmbedBuilder()
                .setTitle('🎮 Game Cancelled')
                .setDescription('The game has been cancelled.')
                .setColor('#ff0000');
            return interaction.update({ 
                embeds: [cancelEmbed], 
                components: [] 
            });
        }

        // Handle moves (rock/paper/scissors)
        if (['rock', 'paper', 'scissors'].includes(action)) {
            if (game.moves[userId]) {
                return interaction.reply({ 
                    content: '❌ You have already made your move!', 
                    ephemeral: true 
                });
            }

            game.moves[userId] = action;

            // Check if both players have moved
            if (Object.keys(game.moves).length === 2) {
                const challengerMove = game.moves[challenger];
                const opponentMove = game.moves[opponent];
                const winner = this.determineWinner(challengerMove, opponentMove);
                
                const emojiMap = { rock: '🪨', paper: '📄', scissors: '✂️' };
                
                const resultEmbed = new EmbedBuilder()
                    .setTitle('🎮 Game Results')
                    .setDescription(
                        winner === 'tie' 
                            ? "🤝 It's a tie!" 
                            : `🎉 <@${winner === 'challenger' ? challenger : opponent}> wins!`
                    )
                    .addFields(
                        { 
                            name: interaction.client.users.cache.get(challenger).displayName, 
                            value: emojiMap[challengerMove], 
                            inline: true 
                        },
                        { name: 'VS', value: '⚔️', inline: true },
                        { 
                            name: interaction.client.users.cache.get(opponent).displayName, 
                            value: emojiMap[opponentMove], 
                            inline: true 
                        }
                    )
                    .setColor(winner === 'tie' ? '#ffaa00' : '#00ff00')
                    .setTimestamp();

                activeGames.delete(gameId);
                return interaction.update({ 
                    embeds: [resultEmbed], 
                    components: [] 
                });
            } else {
                // Still waiting for the other player
                const waitingFor = userId === challenger ? opponent : challenger;
                const waitingUser = interaction.client.users.cache.get(waitingFor);
                
                const waitingEmbed = new EmbedBuilder()
                    .setTitle('🎮 Waiting...')
                    .setDescription(`${interaction.user.displayName} has made their move. Waiting for ${waitingUser.displayName}...`)
                    .setColor('#ffaa00');

                return interaction.update({ 
                    embeds: [waitingEmbed], 
                    components: interaction.message.components 
                });
            }
        }
    },

    determineWinner(move1, move2) {
        if (move1 === move2) return 'tie';
        
        const winConditions = {
            rock: 'scissors',
            paper: 'rock',
            scissors: 'paper'
        };
        
        return winConditions[move1] === move2 ? 'challenger' : 'opponent';
    }
};
