const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const config = require('../../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('automod')
        .setDescription('Manage automod settings')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check automod status and configuration')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('toggle')
                .setDescription('Enable or disable automod')
                .addBooleanOption(option =>
                    option
                        .setName('enabled')
                        .setDescription('Enable or disable automod')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('warnings')
                .setDescription('Check or clear user warnings')
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setDescription('User to check warnings for')
                        .setRequired(true)
                )
                .addBooleanOption(option =>
                    option
                        .setName('clear')
                        .setDescription('Clear the user\'s warnings')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Configure basic automod settings')
                .addIntegerOption(option =>
                    option
                        .setName('max_warnings')
                        .setDescription('Maximum warnings before timeout (1-10)')
                        .setMinValue(1)
                        .setMaxValue(10)
                        .setRequired(false)
                )
                .addChannelOption(option =>
                    option
                        .setName('log_channel')
                        .setDescription('Channel to log deleted messages')
                        .setRequired(false)
                )
                .addChannelOption(option =>
                    option
                        .setName('alert_channel')
                        .setDescription('Channel for critical violation alerts')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('dm_users')
                        .setDescription('Send DM notifications to users')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('advanced_logging')
                        .setDescription('Enable detailed anonymized logging')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('detection')
                .setDescription('Configure detection settings')
                .addBooleanOption(option =>
                    option
                        .setName('profanity_detection')
                        .setDescription('Enable profanity detection (fuck, shit, bitch, etc.)')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('toxicity_detection')
                        .setDescription('Enable toxicity detection (insults, bullying, harassment)')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('sexual_content_detection')
                        .setDescription('Enable sexual content detection')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('spam_detection')
                        .setDescription('Enable spam detection')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('link_filtering')
                        .setDescription('Enable link filtering')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('context_analysis')
                        .setDescription('Enable context-aware filtering')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option
                        .setName('rate_limiting')
                        .setDescription('Enable rate limiting')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('severity')
                .setDescription('Configure severity-based punishment system')
                .addStringOption(option =>
                    option
                        .setName('level')
                        .setDescription('Severity level to configure')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Low Severity', value: 'low' },
                            { name: 'Medium Severity', value: 'medium' },
                            { name: 'High Severity', value: 'high' },
                            { name: 'Critical Severity', value: 'critical' }
                        )
                )
                .addStringOption(option =>
                    option
                        .setName('action')
                        .setDescription('Action to take for this severity')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Warn Only', value: 'warn' },
                            { name: 'Delete Message', value: 'delete' },
                            { name: 'Timeout User', value: 'timeout' }
                        )
                )
                .addIntegerOption(option =>
                    option
                        .setName('duration')
                        .setDescription('Timeout duration in minutes (0 for no timeout)')
                        .setMinValue(0)
                        .setMaxValue(1440)
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option
                        .setName('warning_weight')
                        .setDescription('How much this adds to warning score (1-10)')
                        .setMinValue(1)
                        .setMaxValue(10)
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('profanity')
                .setDescription('Configure profanity filtering settings')
                .addStringOption(option =>
                    option
                        .setName('filter_level')
                        .setDescription('Profanity filter strictness level')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Low (Basic profanity only)', value: 'low' },
                            { name: 'Medium (Most profanity)', value: 'medium' },
                            { name: 'High (Strict filtering)', value: 'high' },
                            { name: 'Strict (Maximum filtering)', value: 'strict' }
                        )
                )
                .addBooleanOption(option =>
                    option
                        .setName('warn_on_mild')
                        .setDescription('Warn on mild profanity instead of deleting')
                        .setRequired(false)
                )
                .addChannelOption(option =>
                    option
                        .setName('allowed_channel')
                        .setDescription('Add/remove channel where profanity is allowed')
                        .setRequired(false)
                )
                .addRoleOption(option =>
                    option
                        .setName('bypass_role')
                        .setDescription('Add/remove role that can bypass profanity filter')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option
                        .setName('action')
                        .setDescription('Action for channel/role management')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Add', value: 'add' },
                            { name: 'Remove', value: 'remove' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('spam')
                .setDescription('Configure spam detection settings')
                .addIntegerOption(option =>
                    option
                        .setName('max_duplicates')
                        .setDescription('Max duplicate messages allowed (1-10)')
                        .setMinValue(1)
                        .setMaxValue(10)
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option
                        .setName('max_messages_per_minute')
                        .setDescription('Max messages per minute (1-30)')
                        .setMinValue(1)
                        .setMaxValue(30)
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option
                        .setName('max_caps_percentage')
                        .setDescription('Max caps percentage (10-100)')
                        .setMinValue(10)
                        .setMaxValue(100)
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option
                        .setName('max_emojis')
                        .setDescription('Max emojis per message (1-20)')
                        .setMinValue(1)
                        .setMaxValue(20)
                        .setRequired(false)
                )
                .addIntegerOption(option =>
                    option
                        .setName('max_mentions')
                        .setDescription('Max mentions per message (1-10)')
                        .setMinValue(1)
                        .setMaxValue(10)
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('View server violation statistics')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('exemptions')
                .setDescription('Manage role and channel exemptions')
                .addStringOption(option =>
                    option
                        .setName('action')
                        .setDescription('Action to perform')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Add', value: 'add' },
                            { name: 'Remove', value: 'remove' },
                            { name: 'List', value: 'list' }
                        )
                )
                .addRoleOption(option =>
                    option
                        .setName('exempt_role')
                        .setDescription('Add/remove exempt role')
                        .setRequired(false)
                )
                .addRoleOption(option =>
                    option
                        .setName('trusted_role')
                        .setDescription('Add/remove trusted role (reduced restrictions)')
                        .setRequired(false)
                )
                .addChannelOption(option =>
                    option
                        .setName('exempt_channel')
                        .setDescription('Add/remove exempt channel')
                        .setRequired(false)
                )
        ),
    name: 'automod',
    category: 'Moderation',
    aliases: [],
    cooldown: 5,
    usage: '<subcommand> [options]',
    description: 'Manage automod settings for content filtering',
    memberpermissions: [PermissionFlagsBits.ManageGuild],
    botpermissions: [PermissionFlagsBits.ManageMessages, PermissionFlagsBits.ModerateMembers],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: -1,
    nsfw: false,
    BotOwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    
    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();
        
        // Get the automod module from the client
        const automod = interaction.client.automod;
        if (!automod) {
            return interaction.reply({
                content: '❌ Automod module is not available.',
                ephemeral: true
            });
        }
        
        switch (subcommand) {
            case 'status':
                await this.handleStatus(interaction, automod);
                break;
            case 'toggle':
                await this.handleToggle(interaction, automod);
                break;
            case 'warnings':
                await this.handleWarnings(interaction, automod);
                break;
            case 'config':
                await this.handleConfig(interaction, automod);
                break;
            case 'detection':
                await this.handleDetection(interaction, automod);
                break;
            case 'severity':
                await this.handleSeverity(interaction, automod);
                break;
            case 'profanity':
                await this.handleProfanity(interaction, automod);
                break;
            case 'spam':
                await this.handleSpam(interaction, automod);
                break;
            case 'stats':
                await this.handleStats(interaction, automod);
                break;
            case 'exemptions':
                await this.handleExemptions(interaction, automod);
                break;
        }
    },
    
    async handleStatus(interaction, automod) {
        const config = automod.config;

        const embed = new EmbedBuilder()
            .setColor(config.enabled ? '#00ff00' : '#ff0000')
            .setTitle('🛡️ Advanced Automod Status')
            .addFields(
                { name: '🔧 System Status', value: config.enabled ? '✅ Enabled' : '❌ Disabled', inline: true },
                { name: '⚠️ Max Warnings', value: config.maxWarnings.toString(), inline: true },
                { name: '📊 Severity System', value: config.severitySystem.enabled ? '✅ Enabled' : '❌ Disabled', inline: true },
                { name: '📝 Log Channel', value: config.logChannelId ? `<#${config.logChannelId}>` : 'Not set', inline: true },
                { name: '🚨 Alert Channel', value: config.alertChannelId ? `<#${config.alertChannelId}>` : 'Not set', inline: true },
                { name: '💬 DM Users', value: config.dmUsers ? '✅ Yes' : '❌ No', inline: true },
                { name: '🔍 Content Detection', value: [
                    `Racist Content: ${config.detectRacistContent?.enabled ? '✅' : '❌'}`,
                    `Death Threats: ${config.detectDeathThreats?.enabled ? '✅' : '❌'}`,
                    `Hate Speech: ${config.detectHateSpeech?.enabled ? '✅' : '❌'}`,
                    `Profanity: ${config.detectProfanity?.enabled ? '✅' : '❌'}`,
                    `Sexual Content: ${config.detectSexualContent?.enabled ? '✅' : '❌'}`,
                    `Toxicity: ${config.detectToxicity?.enabled ? '✅' : '❌'}`
                ].join('\n'), inline: true },
                { name: '🔧 Advanced Features', value: [
                    `Context Analysis: ${config.enableContextAnalysis ? '✅' : '❌'}`,
                    `Link Filtering: ${config.enableLinkFiltering ? '✅' : '❌'}`,
                    `Rate Limiting: ${config.enableRateLimiting ? '✅' : '❌'}`,
                    `Advanced Logging: ${config.enableAdvancedLogging ? '✅' : '❌'}`
                ].join('\n'), inline: true },
                { name: '📈 Spam Settings', value: [
                    `Max Duplicates: ${config.spamDetection?.maxDuplicateMessages || 3}`,
                    `Max Messages/Min: ${config.spamDetection?.maxMessagesPerMinute || 10}`,
                    `Max Caps %: ${config.spamDetection?.maxCapsPercentage || 70}%`,
                    `Max Emojis: ${config.spamDetection?.maxEmojisPerMessage || 5}`
                ].join('\n'), inline: true }
            )
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },
    
    async handleToggle(interaction, automod) {
        const enabled = interaction.options.getBoolean('enabled');
        
        automod.updateConfig({ enabled });
        
        const embed = new EmbedBuilder()
            .setColor(enabled ? '#00ff00' : '#ff0000')
            .setTitle('🛡️ Automod Updated')
            .setDescription(`Automod has been ${enabled ? 'enabled' : 'disabled'}.`)
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Automod' });
        
        await interaction.reply({ embeds: [embed], ephemeral: true });
    },
    
    async handleWarnings(interaction, automod) {
        const user = interaction.options.getUser('user');
        const clear = interaction.options.getBoolean('clear') || false;

        const warnings = automod.getUserWarnings(interaction.guildId, user.id);

        if (clear) {
            automod.clearUserWarnings(interaction.guildId, user.id);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🧹 Warnings Cleared')
                .setDescription(`Cleared all warnings and score for ${user.tag}`)
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            await interaction.reply({ embeds: [embed], ephemeral: true });
        } else {
            const embed = new EmbedBuilder()
                .setColor(warnings.score > 0 ? '#ff9900' : '#00ff00')
                .setTitle('⚠️ User Warning Profile')
                .addFields(
                    { name: 'User', value: user.tag, inline: true },
                    { name: 'Warning Score', value: `${warnings.score}/${automod.config.maxWarnings}`, inline: true },
                    { name: 'Total Violations', value: warnings.count.toString(), inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            if (warnings.violations.length > 0) {
                const recentViolations = warnings.violations
                    .slice(-5) // Show last 5 violations
                    .map(v => {
                        const severityEmoji = {
                            'low': '🟡',
                            'medium': '🟠',
                            'high': '🔴',
                            'critical': '🚨'
                        };
                        return `${severityEmoji[v.severity] || '⚠️'} ${v.type} (${v.severity}) - ${v.timestamp.toLocaleString()}`;
                    })
                    .join('\n');

                embed.addFields({
                    name: 'Recent Violations',
                    value: recentViolations,
                    inline: false
                });

                // Add severity breakdown
                const severityCounts = warnings.violations.reduce((acc, v) => {
                    acc[v.severity] = (acc[v.severity] || 0) + 1;
                    return acc;
                }, {});

                const severityBreakdown = Object.entries(severityCounts)
                    .map(([severity, count]) => `${severity}: ${count}`)
                    .join(', ');

                embed.addFields({
                    name: 'Severity Breakdown',
                    value: severityBreakdown,
                    inline: false
                });
            }

            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    },
    
    async handleConfig(interaction, automod) {
        const maxWarnings = interaction.options.getInteger('max_warnings');
        const logChannel = interaction.options.getChannel('log_channel');
        const alertChannel = interaction.options.getChannel('alert_channel');
        const dmUsers = interaction.options.getBoolean('dm_users');
        const advancedLogging = interaction.options.getBoolean('advanced_logging');

        const updates = {};
        const changes = [];

        if (maxWarnings !== null) {
            updates.maxWarnings = maxWarnings;
            changes.push(`Max warnings: ${maxWarnings}`);
        }

        if (logChannel !== null) {
            updates.logChannelId = logChannel.id;
            changes.push(`Log channel: ${logChannel}`);
        }

        if (alertChannel !== null) {
            updates.alertChannelId = alertChannel.id;
            changes.push(`Alert channel: ${alertChannel}`);
        }

        if (dmUsers !== null) {
            updates.dmUsers = dmUsers;
            changes.push(`DM users: ${dmUsers ? 'enabled' : 'disabled'}`);
        }

        if (advancedLogging !== null) {
            updates.enableAdvancedLogging = advancedLogging;
            changes.push(`Advanced logging: ${advancedLogging ? 'enabled' : 'disabled'}`);
        }

        if (changes.length === 0) {
            return interaction.reply({
                content: '❌ No configuration changes specified.',
                ephemeral: true
            });
        }

        automod.updateConfig(updates);

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('⚙️ Basic Configuration Updated')
            .setDescription('The following settings have been updated:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleDetection(interaction, automod) {
        const profanityDetection = interaction.options.getBoolean('profanity_detection');
        const toxicityDetection = interaction.options.getBoolean('toxicity_detection');
        const sexualContentDetection = interaction.options.getBoolean('sexual_content_detection');
        const spamDetection = interaction.options.getBoolean('spam_detection');
        const linkFiltering = interaction.options.getBoolean('link_filtering');
        const contextAnalysis = interaction.options.getBoolean('context_analysis');
        const rateLimiting = interaction.options.getBoolean('rate_limiting');

        const updates = {};
        const changes = [];

        if (profanityDetection !== null) {
            updates['detectProfanity.enabled'] = profanityDetection;
            changes.push(`Profanity detection: ${profanityDetection ? 'enabled' : 'disabled'}`);
        }

        if (toxicityDetection !== null) {
            updates['detectToxicity.enabled'] = toxicityDetection;
            changes.push(`Toxicity detection: ${toxicityDetection ? 'enabled' : 'disabled'}`);
        }

        if (sexualContentDetection !== null) {
            updates['detectSexualContent.enabled'] = sexualContentDetection;
            changes.push(`Sexual content detection: ${sexualContentDetection ? 'enabled' : 'disabled'}`);
        }

        if (spamDetection !== null) {
            updates.enableSpamDetection = spamDetection;
            changes.push(`Spam detection: ${spamDetection ? 'enabled' : 'disabled'}`);
        }

        if (linkFiltering !== null) {
            updates.enableLinkFiltering = linkFiltering;
            changes.push(`Link filtering: ${linkFiltering ? 'enabled' : 'disabled'}`);
        }

        if (contextAnalysis !== null) {
            updates.enableContextAnalysis = contextAnalysis;
            changes.push(`Context analysis: ${contextAnalysis ? 'enabled' : 'disabled'}`);
        }

        if (rateLimiting !== null) {
            updates.enableRateLimiting = rateLimiting;
            changes.push(`Rate limiting: ${rateLimiting ? 'enabled' : 'disabled'}`);
        }

        if (changes.length === 0) {
            return interaction.reply({
                content: '❌ No detection settings specified.',
                ephemeral: true
            });
        }

        // Apply nested updates
        for (const [key, value] of Object.entries(updates)) {
            if (key.includes('.')) {
                const keys = key.split('.');
                let target = automod.config;
                for (let i = 0; i < keys.length - 1; i++) {
                    target = target[keys[i]];
                }
                target[keys[keys.length - 1]] = value;
            } else {
                automod.config[key] = value;
            }
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🔍 Detection Settings Updated')
            .setDescription('The following detection settings have been updated:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleSeverity(interaction, automod) {
        const level = interaction.options.getString('level');
        const action = interaction.options.getString('action');
        const duration = interaction.options.getInteger('duration');
        const warningWeight = interaction.options.getInteger('warning_weight');

        const severityKey = level + 'Severity';
        const currentConfig = automod.config.severitySystem[severityKey];

        if (!currentConfig) {
            return interaction.reply({
                content: '❌ Invalid severity level.',
                ephemeral: true
            });
        }

        const updates = {};
        const changes = [];

        if (action !== null) {
            updates[`severitySystem.${severityKey}.action`] = action;
            changes.push(`Action: ${action}`);
        }

        if (duration !== null) {
            updates[`severitySystem.${severityKey}.duration`] = duration;
            changes.push(`Duration: ${duration} minutes`);
        }

        if (warningWeight !== null) {
            updates[`severitySystem.${severityKey}.warningWeight`] = warningWeight;
            changes.push(`Warning weight: ${warningWeight}`);
        }

        if (changes.length === 0) {
            // Show current configuration
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`⚖️ ${level.charAt(0).toUpperCase() + level.slice(1)} Severity Configuration`)
                .addFields(
                    { name: 'Action', value: currentConfig.action, inline: true },
                    { name: 'Duration', value: `${currentConfig.duration} minutes`, inline: true },
                    { name: 'Warning Weight', value: currentConfig.warningWeight.toString(), inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // Apply nested updates
        for (const [key, value] of Object.entries(updates)) {
            const keys = key.split('.');
            let target = automod.config;
            for (let i = 0; i < keys.length - 1; i++) {
                target = target[keys[i]];
            }
            target[keys[keys.length - 1]] = value;
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle(`⚖️ ${level.charAt(0).toUpperCase() + level.slice(1)} Severity Updated`)
            .setDescription('The following settings have been updated:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleProfanity(interaction, automod) {
        const filterLevel = interaction.options.getString('filter_level');
        const warnOnMild = interaction.options.getBoolean('warn_on_mild');
        const allowedChannel = interaction.options.getChannel('allowed_channel');
        const bypassRole = interaction.options.getRole('bypass_role');
        const action = interaction.options.getString('action');

        const updates = {};
        const changes = [];

        if (filterLevel !== null) {
            updates['profanityFiltering.filterLevel'] = filterLevel;
            changes.push(`Filter level: ${filterLevel}`);
        }

        if (warnOnMild !== null) {
            updates['profanityFiltering.warnOnMildProfanity'] = warnOnMild;
            changes.push(`Warn on mild profanity: ${warnOnMild ? 'enabled' : 'disabled'}`);
        }

        if (allowedChannel && action) {
            const currentChannels = automod.config.profanityFiltering.allowedChannels || [];
            if (action === 'add') {
                if (!currentChannels.includes(allowedChannel.id)) {
                    updates['profanityFiltering.allowedChannels'] = [...currentChannels, allowedChannel.id];
                    changes.push(`Added allowed channel: ${allowedChannel.name}`);
                } else {
                    changes.push(`Channel ${allowedChannel.name} already allows profanity`);
                }
            } else if (action === 'remove') {
                updates['profanityFiltering.allowedChannels'] = currentChannels.filter(id => id !== allowedChannel.id);
                changes.push(`Removed allowed channel: ${allowedChannel.name}`);
            }
        }

        if (bypassRole && action) {
            const currentRoles = automod.config.profanityFiltering.bypassRoles || [];
            if (action === 'add') {
                if (!currentRoles.includes(bypassRole.id)) {
                    updates['profanityFiltering.bypassRoles'] = [...currentRoles, bypassRole.id];
                    changes.push(`Added bypass role: ${bypassRole.name}`);
                } else {
                    changes.push(`Role ${bypassRole.name} already bypasses profanity filter`);
                }
            } else if (action === 'remove') {
                updates['profanityFiltering.bypassRoles'] = currentRoles.filter(id => id !== bypassRole.id);
                changes.push(`Removed bypass role: ${bypassRole.name}`);
            }
        }

        if (changes.length === 0) {
            // Show current profanity settings
            const profanityConfig = automod.config.profanityFiltering;
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('🚫 Current Profanity Filter Settings')
                .addFields(
                    { name: 'Filter Level', value: profanityConfig.filterLevel || 'medium', inline: true },
                    { name: 'Warn on Mild', value: profanityConfig.warnOnMildProfanity ? 'Yes' : 'No', inline: true },
                    { name: 'Strict Mode', value: profanityConfig.strictMode ? 'Yes' : 'No', inline: true },
                    {
                        name: 'Allowed Channels',
                        value: profanityConfig.allowedChannels?.length > 0
                            ? profanityConfig.allowedChannels.map(id => `<#${id}>`).join('\n')
                            : 'None',
                        inline: true
                    },
                    {
                        name: 'Bypass Roles',
                        value: profanityConfig.bypassRoles?.length > 0
                            ? profanityConfig.bypassRoles.map(id => `<@&${id}>`).join('\n')
                            : 'None',
                        inline: true
                    }
                )
                .setDescription('**Filter Levels:**\n• Low: Basic profanity only\n• Medium: Most profanity\n• High: Strict filtering\n• Strict: Maximum filtering')
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // Apply nested updates
        for (const [key, value] of Object.entries(updates)) {
            const keys = key.split('.');
            let target = automod.config;
            for (let i = 0; i < keys.length - 1; i++) {
                target = target[keys[i]];
            }
            target[keys[keys.length - 1]] = value;
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🚫 Profanity Filter Settings Updated')
            .setDescription('The following profanity settings have been updated:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleSpam(interaction, automod) {
        const maxDuplicates = interaction.options.getInteger('max_duplicates');
        const maxMessagesPerMinute = interaction.options.getInteger('max_messages_per_minute');
        const maxCapsPercentage = interaction.options.getInteger('max_caps_percentage');
        const maxEmojis = interaction.options.getInteger('max_emojis');
        const maxMentions = interaction.options.getInteger('max_mentions');

        const updates = {};
        const changes = [];

        if (maxDuplicates !== null) {
            updates['spamDetection.maxDuplicateMessages'] = maxDuplicates;
            changes.push(`Max duplicates: ${maxDuplicates}`);
        }

        if (maxMessagesPerMinute !== null) {
            updates['spamDetection.maxMessagesPerMinute'] = maxMessagesPerMinute;
            changes.push(`Max messages/minute: ${maxMessagesPerMinute}`);
        }

        if (maxCapsPercentage !== null) {
            updates['spamDetection.maxCapsPercentage'] = maxCapsPercentage;
            changes.push(`Max caps percentage: ${maxCapsPercentage}%`);
        }

        if (maxEmojis !== null) {
            updates['spamDetection.maxEmojisPerMessage'] = maxEmojis;
            changes.push(`Max emojis: ${maxEmojis}`);
        }

        if (maxMentions !== null) {
            updates['spamDetection.maxMentionsPerMessage'] = maxMentions;
            changes.push(`Max mentions: ${maxMentions}`);
        }

        if (changes.length === 0) {
            // Show current spam settings
            const spamConfig = automod.config.spamDetection;
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('📈 Current Spam Detection Settings')
                .addFields(
                    { name: 'Max Duplicate Messages', value: spamConfig.maxDuplicateMessages.toString(), inline: true },
                    { name: 'Max Messages/Minute', value: spamConfig.maxMessagesPerMinute.toString(), inline: true },
                    { name: 'Max Caps Percentage', value: `${spamConfig.maxCapsPercentage}%`, inline: true },
                    { name: 'Max Emojis/Message', value: spamConfig.maxEmojisPerMessage.toString(), inline: true },
                    { name: 'Max Mentions/Message', value: spamConfig.maxMentionsPerMessage.toString(), inline: true },
                    { name: 'Duplicate Timeframe', value: `${spamConfig.duplicateTimeframe / 1000} seconds`, inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // Apply nested updates
        for (const [key, value] of Object.entries(updates)) {
            const keys = key.split('.');
            let target = automod.config;
            for (let i = 0; i < keys.length - 1; i++) {
                target = target[keys[i]];
            }
            target[keys[keys.length - 1]] = value;
        }

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('📈 Spam Detection Settings Updated')
            .setDescription('The following spam settings have been updated:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleStats(interaction, automod) {
        const stats = automod.getViolationStats(interaction.guildId);

        if (Object.keys(stats).length === 0) {
            return interaction.reply({
                content: '📊 No violation statistics available yet.',
                ephemeral: true
            });
        }

        const totalViolations = Object.values(stats).reduce((sum, count) => sum + count, 0);
        const sortedStats = Object.entries(stats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10); // Top 10 violation types

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📊 Server Violation Statistics')
            .setDescription(`Total violations detected: **${totalViolations}**`)
            .addFields({
                name: 'Violation Breakdown',
                value: sortedStats.map(([type, count]) =>
                    `**${type}**: ${count} (${((count / totalViolations) * 100).toFixed(1)}%)`
                ).join('\n') || 'No data available',
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    },

    async handleExemptions(interaction, automod) {
        const exemptRole = interaction.options.getRole('exempt_role');
        const trustedRole = interaction.options.getRole('trusted_role');
        const exemptChannel = interaction.options.getChannel('exempt_channel');
        const action = interaction.options.getString('action');

        if (action === 'list') {
            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('🛡️ Current Exemptions')
                .addFields(
                    {
                        name: 'Exempt Roles',
                        value: automod.config.exemptRoles.length > 0
                            ? automod.config.exemptRoles.map(id => `<@&${id}>`).join('\n')
                            : 'None',
                        inline: true
                    },
                    {
                        name: 'Trusted Roles',
                        value: automod.config.trustedRoles.length > 0
                            ? automod.config.trustedRoles.map(id => `<@&${id}>`).join('\n')
                            : 'None',
                        inline: true
                    },
                    {
                        name: 'Exempt Channels',
                        value: automod.config.exemptChannels.length > 0
                            ? automod.config.exemptChannels.map(id => `<#${id}>`).join('\n')
                            : 'None',
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'RankBreaker Advanced Automod' });

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        const updates = {};
        const changes = [];

        if (exemptRole) {
            const currentExempt = automod.config.exemptRoles || [];
            if (action === 'add') {
                if (!currentExempt.includes(exemptRole.id)) {
                    updates.exemptRoles = [...currentExempt, exemptRole.id];
                    changes.push(`Added exempt role: ${exemptRole.name}`);
                } else {
                    changes.push(`Role ${exemptRole.name} is already exempt`);
                }
            } else if (action === 'remove') {
                updates.exemptRoles = currentExempt.filter(id => id !== exemptRole.id);
                changes.push(`Removed exempt role: ${exemptRole.name}`);
            }
        }

        if (trustedRole) {
            const currentTrusted = automod.config.trustedRoles || [];
            if (action === 'add') {
                if (!currentTrusted.includes(trustedRole.id)) {
                    updates.trustedRoles = [...currentTrusted, trustedRole.id];
                    changes.push(`Added trusted role: ${trustedRole.name}`);
                } else {
                    changes.push(`Role ${trustedRole.name} is already trusted`);
                }
            } else if (action === 'remove') {
                updates.trustedRoles = currentTrusted.filter(id => id !== trustedRole.id);
                changes.push(`Removed trusted role: ${trustedRole.name}`);
            }
        }

        if (exemptChannel) {
            const currentExemptChannels = automod.config.exemptChannels || [];
            if (action === 'add') {
                if (!currentExemptChannels.includes(exemptChannel.id)) {
                    updates.exemptChannels = [...currentExemptChannels, exemptChannel.id];
                    changes.push(`Added exempt channel: ${exemptChannel.name}`);
                } else {
                    changes.push(`Channel ${exemptChannel.name} is already exempt`);
                }
            } else if (action === 'remove') {
                updates.exemptChannels = currentExemptChannels.filter(id => id !== exemptChannel.id);
                changes.push(`Removed exempt channel: ${exemptChannel.name}`);
            }
        }

        if (changes.length === 0) {
            return interaction.reply({
                content: '❌ No exemption changes specified.',
                ephemeral: true
            });
        }

        automod.updateConfig(updates);

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🛡️ Exemptions Updated')
            .setDescription('The following exemption changes have been made:')
            .addFields({
                name: 'Changes',
                value: changes.join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Advanced Automod' });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
};
