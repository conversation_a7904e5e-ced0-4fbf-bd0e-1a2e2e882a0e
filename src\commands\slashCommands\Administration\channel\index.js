const fs = require('fs');
const path = require('path');
const { SlashCommandBuilder } = require('discord.js');

// Session management for permission operations to prevent concurrent operations
const permissionSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds

const command = {
    data: new SlashCommandBuilder()
        .setName('channel')
        .setDescription('Channel management commands')
        .setDefaultMemberPermissions(null), // Available to everyone

    name: 'channel',
    category: 'admin',
    aliases: [],
    cooldown: 10,
    usage: '/channel permission | /channel info',
    description: 'Channel management commands including bulk permission editing and detailed channel information',
    memberpermissions: [], // Info is available to all, permission subcommand checks internally
    botpermissions: ['ManageChannels', 'ManageRoles'],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'guild',

    // Map subcommand name → module
    subcommands: new Map(),

    // Build subcommands and populate subcommands map
    registerSubcommands() {
        const folder = __dirname;
        const files = fs.readdirSync(folder)
            .filter(f => f !== 'index.js' && f.endsWith('.js'));
        
        for (const file of files) {
            try {
                const mod = require(path.join(folder, file));
                // Each module must export .data as a SlashCommandSubcommandBuilder
                if (mod.data && mod.data.name) {
                    this.data.addSubcommand(mod.data);
                    this.subcommands.set(mod.data.name, mod);
                    console.log(`✅ Loaded channel subcommand: ${mod.data.name}`);
                }
            } catch (error) {
                console.error(`❌ Error loading channel module ${file}:`, error);
            }
        }
    },

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();
        const mod = this.subcommands.get(subcommand);
        
        if (!mod) {
            return interaction.reply({ 
                content: `❌ Unknown subcommand: ${subcommand}. Available subcommands: ${Array.from(this.subcommands.keys()).join(', ')}`, 
                ephemeral: true 
            });
        }

        // Pass shared resources to subcommands
        return mod.execute(interaction, { permissionSessions, SESSION_TIMEOUT });
    },

    // Dispatched from global interactionCreate handler
    async handleButtonInteraction(interaction) {
        const customId = interaction.customId;
        
        // Route to appropriate subcommand based on customId prefix
        if (customId.startsWith('channelinfo_')) {
            const infoMod = this.subcommands.get('info');
            if (infoMod && typeof infoMod.handleButtonInteraction === 'function') {
                return infoMod.handleButtonInteraction(interaction);
            }
        } else if (customId.includes('channelperm_')) {
            const permissionMod = this.subcommands.get('permission');
            if (permissionMod && typeof permissionMod.handleButtonInteraction === 'function') {
                return permissionMod.handleButtonInteraction(interaction, { permissionSessions, SESSION_TIMEOUT });
            }
        }

        console.log(`No button handler found for channel customId: ${customId}`);
        return interaction.reply({ 
            content: '❌ No handler found for this button interaction.', 
            ephemeral: true 
        });
    }
};

// Register subcommands before export
command.registerSubcommands();

// Cleanup interval for expired sessions
setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of permissionSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            permissionSessions.delete(userId);
        }
    }
}, 60000); // Check every minute

module.exports = command;
