const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const config = require('../../../../config/config.js')
const path = require('path');

const cmdname = path.parse(__filename).name; // file name
const folderName = path.basename(__dirname); // folder name
module.exports = {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Ping! A simple latency command.'),
    name: cmdname,
    category: folderName,
    aliases: ['pong', 'latency'],
    cooldown: 30, 
    usage: '<user>',
    description: 'Ping! A simple latency command.',
    memberpermissions: [], 
    botpermissions: [],
    requiredroles: [], 
    requiredchannels: [], 
    alloweduserids: [], 
    minargs: 0,
    maxargs: 0,
    nsfw: false, 
    OwnerOnly: true, 
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'user', // This simple utility command works well as a user app
    async execute(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🏓 Pong!')
            .setColor(config.EmbedConfig.embedcolor) 
            .addFields(
                { name: 'Bot Latency', value: `${Date.now() - interaction.createdTimestamp}ms`, inline: true },
                { name: 'API Latency', value: `${Math.round(interaction.client.ws.ping)}ms`, inline: true }
            )
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    },
};
