const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, Embed<PERSON><PERSON>er, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('servermanager')
        .setDescription('Manage automatic server management settings')
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Show current server management status and configuration'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Update server management configuration')
                .addIntegerOption(option =>
                    option.setName('minimum_members')
                        .setDescription('Minimum member count required (default: 50)')
                        .setMinValue(10)
                        .setMaxValue(1000))
                .addIntegerOption(option =>
                    option.setName('check_interval')
                        .setDescription('Check interval in seconds (default: 10)')
                        .setMinValue(5)
                        .setMaxValue(300))
                .addBooleanOption(option =>
                    option.setName('enabled')
                        .setDescription('Enable or disable automatic server management')))
        .addSubcommand(subcommand =>
            subcommand
                .setName('whitelist')
                .setDescription('Manage whitelisted servers')
                .addStringOption(option =>
                    option.setName('action')
                        .setDescription('Action to perform')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Add Server', value: 'add' },
                            { name: 'Remove Server', value: 'remove' },
                            { name: 'List Servers', value: 'list' }
                        ))
                .addStringOption(option =>
                    option.setName('server_id')
                        .setDescription('Server ID (required for add/remove actions)')))
        .addSubcommand(subcommand =>
            subcommand
                .setName('check')
                .setDescription('Manually trigger a server check'))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    name: 'servermanager',
    category: 'admin',
    aliases: [],
    cooldown: 30,
    usage: '/servermanager <subcommand>',
    description: 'Manage automatic server management settings',
    memberpermissions: ['Administrator'],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'guild',

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();
        const serverManager = interaction.client.serverManager;

        if (!serverManager) {
            return await interaction.reply({
                content: '❌ Server management system is not available.',
                ephemeral: true
            });
        }

        switch (subcommand) {
            case 'status':
                await this.handleStatus(interaction, serverManager);
                break;
            case 'config':
                await this.handleConfig(interaction, serverManager);
                break;
            case 'whitelist':
                await this.handleWhitelist(interaction, serverManager);
                break;
            case 'check':
                await this.handleCheck(interaction, serverManager);
                break;
        }
    },

    async handleStatus(interaction, serverManager) {
        const config = serverManager.config;
        const guilds = interaction.client.guilds.cache;

        const statusEmbed = new EmbedBuilder()
            .setTitle('🔧 Server Management Status')
            .setDescription('Current configuration and statistics for automatic server management')
            .addFields(
                { name: '⚙️ Status', value: config.enabled ? '🟢 Enabled' : '🔴 Disabled', inline: true },
                { name: '👥 Minimum Members', value: `${config.minimumMembers}`, inline: true },
                { name: '⏱️ Check Interval', value: `${config.checkInterval / 1000}s`, inline: true },
                { name: '🛡️ Whitelisted Servers', value: `${config.whitelistedServers.length}`, inline: true },
                { name: '🌐 Total Servers', value: `${guilds.size}`, inline: true },
                { name: '📊 Small Servers', value: `${guilds.filter(g => g.memberCount < config.minimumMembers && !config.whitelistedServers.includes(g.id)).size}`, inline: true }
            )
            .setColor(config.enabled ? '#2ecc71' : '#e74c3c')
            .setTimestamp();

        await interaction.reply({ embeds: [statusEmbed], ephemeral: true });
    },

    async handleConfig(interaction, serverManager) {
        const minimumMembers = interaction.options.getInteger('minimum_members');
        const checkInterval = interaction.options.getInteger('check_interval');
        const enabled = interaction.options.getBoolean('enabled');

        const updates = {};
        if (minimumMembers !== null) updates.minimumMembers = minimumMembers;
        if (checkInterval !== null) updates.checkInterval = checkInterval * 1000; // Convert to milliseconds
        if (enabled !== null) updates.enabled = enabled;

        if (Object.keys(updates).length === 0) {
            return await interaction.reply({
                content: '❌ No configuration changes specified.',
                ephemeral: true
            });
        }

        serverManager.updateConfig(updates);

        const updateEmbed = new EmbedBuilder()
            .setTitle('✅ Configuration Updated')
            .setDescription('Server management configuration has been updated successfully.')
            .setColor('#2ecc71')
            .setTimestamp();

        if (minimumMembers !== null) {
            updateEmbed.addFields({ name: '👥 Minimum Members', value: `${minimumMembers}`, inline: true });
        }
        if (checkInterval !== null) {
            updateEmbed.addFields({ name: '⏱️ Check Interval', value: `${checkInterval}s`, inline: true });
        }
        if (enabled !== null) {
            updateEmbed.addFields({ name: '⚙️ Status', value: enabled ? '🟢 Enabled' : '🔴 Disabled', inline: true });
        }

        await interaction.reply({ embeds: [updateEmbed], ephemeral: true });
    },

    async handleWhitelist(interaction, serverManager) {
        const action = interaction.options.getString('action');
        const serverId = interaction.options.getString('server_id');

        switch (action) {
            case 'add':
                if (!serverId) {
                    return await interaction.reply({
                        content: '❌ Server ID is required for adding to whitelist.',
                        ephemeral: true
                    });
                }
                serverManager.addWhitelistedServer(serverId);
                await interaction.reply({
                    content: `✅ Added server \`${serverId}\` to whitelist.`,
                    ephemeral: true
                });
                break;

            case 'remove':
                if (!serverId) {
                    return await interaction.reply({
                        content: '❌ Server ID is required for removing from whitelist.',
                        ephemeral: true
                    });
                }
                serverManager.removeWhitelistedServer(serverId);
                await interaction.reply({
                    content: `✅ Removed server \`${serverId}\` from whitelist.`,
                    ephemeral: true
                });
                break;

            case 'list':
                const whitelistedServers = serverManager.config.whitelistedServers;
                const listEmbed = new EmbedBuilder()
                    .setTitle('🛡️ Whitelisted Servers')
                    .setDescription(whitelistedServers.length > 0 
                        ? whitelistedServers.map(id => `• \`${id}\``).join('\n')
                        : 'No servers are currently whitelisted.')
                    .setColor('#3498db')
                    .setTimestamp();

                await interaction.reply({ embeds: [listEmbed], ephemeral: true });
                break;
        }
    },

    async handleCheck(interaction, serverManager) {
        await interaction.deferReply({ ephemeral: true });

        try {
            await serverManager.checkAllServers();
            await interaction.editReply({
                content: '✅ Manual server check completed successfully.'
            });
        } catch (error) {
            console.error('Error during manual server check:', error);
            await interaction.editReply({
                content: '❌ Error occurred during server check. Check console for details.'
            });
        }
    }
};
