const {
    SlashCommandSubcommand<PERSON><PERSON><PERSON>,
    EmbedBuilder,
    ActionRowBuilder,
    AttachmentBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');
const { createCanvas, loadImage } = require('canvas');
const path = require('path');
const events = require('events');

// Utility functions merged into this file
const utils = {
    disableButtons(components) {
        for (let x = 0; x < components.length; x++) {
            for (let y = 0; y < components[x].components.length; y++) {
                components[x].components[y] = ButtonBuilder.from(components[x].components[y]);
                components[x].components[y].setDisabled(true);
            }
        }
        return components;
    },

    formatMessage(options, contentMsg) {
        const { message } = options;
        let content = options[contentMsg];

        content = content.replace('{player.tag}', message.author.tag)
            .replace('{player.username}', message.author.username)
            .replace('{player}', `<@!${message.author.id}>`)
            .replace('{player.displayName}', message.author.displayName);
        return content;
    },

    move(pos, direction) {
        if (direction === 'up') return { x: pos.x, y: pos.y - 1 };
        else if (direction === 'down') return { x: pos.x, y: pos.y + 1 };
        else if (direction === 'left') return { x: pos.x - 1, y: pos.y };
        else if (direction === 'right') return { x: pos.x + 1, y: pos.y }
        else return pos;
    },

    oppDirection(direction) {
        if (direction === 'up') return 'down';
        else if (direction === 'down') return 'up';
        else if (direction === 'left') return 'right';
        else if (direction === 'right') return 'left';
    }
};

class TwoZeroFourEight extends events {
    constructor(options = {}) {
        super();

        // Game settings validation
        if (!options.message) throw new TypeError('NO_MESSAGE: No message option was provided.');
        if (!options.isSlashGame) options.isSlashGame = false;
        if (!options.timeoutTime) options.timeoutTime = 60000;
        if (!options.buttonStyle) options.buttonStyle = ButtonStyle.Primary;
        if (typeof options.message !== 'object') throw new TypeError('INVALID_MESSAGE: message option must be an object.');
        if (typeof options.isSlashGame !== 'boolean') throw new TypeError('INVALID_COMMAND_TYPE: isSlashGame option must be a boolean.');
        if (typeof options.timeoutTime !== 'number') throw new TypeError('INVALID_TIME: Timeout time option must be a number.');
        if (options.playerOnlyMessage !== false) {
            if (!options.playerOnlyMessage) options.playerOnlyMessage = 'Only {player} can use these buttons.';
            if (typeof options.playerOnlyMessage !== 'string') throw new TypeError('INVALID_MESSAGE: playerOnlyMessage option must be a string.');
        }

        // Embed settings
        if (!options.embed) options.embed = {};
        if (!options.embed.title) options.embed.title = '2048';
        if (!options.embed.color) options.embed.color = '#5865F2';
        if (!options.embed.fields) options.embed.fields = [];
        if (!options.embed.footer) options.embed.footer = {};
        if (!options.embed.timestamp) options.embed.timestamp = false;
        if (!options.embed.footerEnabled) options.embed.footerEnabled = false;
        if (typeof options.embed !== 'object') throw new TypeError('INVALID_EMBED: embed option must be an object.');
        if (typeof options.embed.title !== 'string') throw new TypeError('INVALID_EMBED: embed title must be a string.');
        if (typeof options.embed.color !== 'string') throw new TypeError('INVALID_EMBED: embed color must be a string.');
        if (typeof options.embed.footerEnabled !== 'boolean') throw new TypeError('INVALID_FOOTER: footerEnabled option must be a boolean.');
        if (typeof options.embed.timestamp !== 'boolean') throw new TypeError('INVALID_TIMESTAMP: timestamp option must be a boolean.');

        // Emoji settings
        if (!options.emojis) options.emojis = {};
        if (!options.emojis.up) options.emojis.up = '⬆️';
        if (!options.emojis.down) options.emojis.down = '⬇️';
        if (!options.emojis.left) options.emojis.left = '⬅️';
        if (!options.emojis.right) options.emojis.right = '➡️';
        if (typeof options.emojis !== 'object') throw new TypeError('INVALID_EMOJIS: emojis option must be an object.');
        if (typeof options.emojis.up !== 'string') throw new TypeError('INVALID_EMOJIS: up emoji must be an string.');
        if (typeof options.emojis.down !== 'string') throw new TypeError('INVALID_EMOJIS: down emoji must be an string.');
        if (typeof options.emojis.left !== 'string') throw new TypeError('INVALID_EMOJIS: left emoji must be an string.');
        if (typeof options.emojis.right !== 'string') throw new TypeError('INVALID_EMOJIS: right emoji must be an string.');

        this.options = options;
        this.message = options.message;
        this.gameBoard = [];
        this.mergedPos = [];
        this.length = 4;
        this.score = 0;

        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                this.gameBoard[y * this.length + x] = 0;
            }
        }
    }

    async sendMessage(content) {
        if (this.options.isSlashGame) return await this.message.editReply(content);
        else return await this.message.channel.send(content);
    }

    async getBoardImage() {
        const canvas = createCanvas(this.length * 100, this.length * 100);
        const ctx = canvas.getContext('2d');
        const imageFilenames = [
            '0.png', '2.png', '4.png', '8.png', '16.png', '32.png', '64.png', '128.png', '256.png', '512.png', '1024.png', '2048.png',
        ];

        const images = {};
        for (let i = 0; i < imageFilenames.length; i++) {
            const imagePath = path.join(__dirname, '2048boardImages', imageFilenames[i]);
            images[i] = await loadImage(imagePath).catch(err => {
                console.error(`Error loading image: ${imagePath}`);
                throw err;
            });
        }

        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                const tileValue = this.gameBoard[y * this.length + x];
                const tileImage = images[tileValue];

                if (tileImage) {
                    ctx.drawImage(tileImage, x * 100, y * 100, 100, 100);
                }
            }
        }

        const buffer = canvas.toBuffer();
        return new AttachmentBuilder(buffer, { name: 'gameboard.png' });
    }

    async startGame() {
        if (this.options.isSlashGame || !this.message.author) {
            if (!this.message.deferred) await this.message.deferReply().catch(e => { });
            this.message.author = this.message.user;
            this.options.isSlashGame = true;
        }
        this.placeRandomTile();
        this.placeRandomTile();

        const embed = new EmbedBuilder()
            .setTitle(this.options.embed.title)
            .setColor(this.options.embed.color)
            .setImage('attachment://gameboard.png')
            .addFields({ name: 'Current Score', value: this.score.toString() })

        if (this.options.embed.timestamp) {
            embed.setTimestamp();
        }

        if (this.options.embed.footerEnabled) {
            embed.setFooter({
                text: this.options.embed.footer.text,
                iconURL: this.options.embed.footer.iconURL
            });
        }

        const up = new ButtonBuilder().setEmoji(this.options.emojis.up).setStyle(this.options.buttonStyle).setCustomId('2048_up');
        const down = new ButtonBuilder().setEmoji(this.options.emojis.down).setStyle(this.options.buttonStyle).setCustomId('2048_down');
        const left = new ButtonBuilder().setEmoji(this.options.emojis.left).setStyle(this.options.buttonStyle).setCustomId('2048_left');
        const right = new ButtonBuilder().setEmoji(this.options.emojis.right).setStyle(this.options.buttonStyle).setCustomId('2048_right');
        const row = new ActionRowBuilder().addComponents(up, down, left, right);

        const msg = await this.sendMessage({ embeds: [embed], components: [row], files: [await this.getBoardImage()] });
        // Don't set up collector here since we're using global button handler
        return msg;
    }

    placeRandomTile() {
        let tilePos = { x: 0, y: 0 };

        do {
            tilePos = { x: parseInt(Math.random() * this.length), y: parseInt(Math.random() * this.length) };
        } while (this.gameBoard[tilePos.y * this.length + tilePos.x] != 0)
        this.gameBoard[tilePos.y * this.length + tilePos.x] = (Math.random() > 0.8 ? 2 : 1);
    }

    async handleButtons(msg) {
        const collector = msg.createMessageComponentCollector({ idle: this.options.timeoutTime });

        collector.on('collect', async btn => {
            await btn.deferUpdate().catch(e => { });
            if (btn.user.id !== this.message.author.id) {
                if (this.options.playerOnlyMessage) btn.followUp({ content: utils.formatMessage(this.options, 'playerOnlyMessage'), flags: 64 });
                return;
            }

            let moved = false;
            this.mergedPos = [];
            const direction = btn.customId.split('_')[1];
            if (direction === 'up' || direction === 'down') moved = this.shiftVertical(direction);
            if (direction === 'left' || direction === 'right') moved = this.shiftHorizontal(direction);

            if (moved) this.placeRandomTile();
            if (this.isGameOver()) return collector.stop();

            const embed = new EmbedBuilder()
                .setTitle(this.options.embed.title)
                .setColor(this.options.embed.color)
                .setImage('attachment://gameboard.png')
                .addFields({ name: 'Current Score', value: this.score.toString() })

            if (this.options.embed.fields) {
                for (const field of this.options.embed.fields) {
                    const replacedValue = field.value
                        .replace('{player.tag}', this.message.author.tag)
                        .replace('{player.username}', this.message.author.username)
                        .replace('{player}', `<@!${this.message.author.id}>`)
                        .replace('{player.displayName}', this.message.author.displayName)
                        .replace('{score}', this.score.toString());

                    embed.addFields({
                        name: field.name,
                        value: replacedValue,
                        inline: field.inline || false,
                    });
                }
            }

            if (this.options.embed.timestamp) {
                embed.setTimestamp();
            }

            if (this.options.embed.footerEnabled) {
                embed.setFooter({
                    text: this.options.embed.footer.text,
                    iconURL: this.options.embed.footer.iconURL
                });
            }

            return msg.edit({ embeds: [embed], files: [await this.getBoardImage()], attachments: [] });
        })

        collector.on('end', (_, reason) => {
            if (reason === 'idle' || reason === 'user') {
                return this.gameOver(msg, this.gameBoard.includes(11)); // 2048 = 2^11
            }
        })
    }

    async gameOver(msg, result) {
        const TwoZeroFourEightGame = { player: this.message.author, score: this.score };
        this.emit('gameOver', { result: (result ? 'win' : 'lose'), ...TwoZeroFourEightGame });

        const embed = new EmbedBuilder()
            .setTitle(this.options.embed.title)
            .setColor(this.options.embed.color)
            .setImage('attachment://gameboard.png')
            .addFields({ name: 'Final Score', value: this.score.toString() })

        if (this.options.embed.timestamp) {
            embed.setTimestamp();
        }

        if (this.options.embed.footerEnabled) {
            embed.setFooter({
                text: this.options.embed.footer.text,
                iconURL: this.options.embed.footer.iconURL
            });
        }

        return msg.edit({ embeds: [embed], components: utils.disableButtons(msg.components), files: [await this.getBoardImage()], attachments: [] });
    }

    isGameOver() {
        let boardFull = true;
        let numMoves = 0;

        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                if (this.gameBoard[y * this.length + x] === 0) boardFull = false;
                const posNum = this.gameBoard[y * this.length + x];

                ['down', 'left', 'right', 'up'].forEach(dir => {
                    const newPos = utils.move({ x, y }, dir);
                    if (this.isInsideBlock(newPos) && (this.gameBoard[newPos.y * this.length + newPos.x] === 0 || this.gameBoard[newPos.y * this.length + newPos.x] === posNum)) numMoves++;
                })
            }
        }
        return (boardFull && numMoves === 0);
    }

    shiftVertical(dir) {
        let moved = false;
        for (let x = 0; x < this.length; x++) {
            if (dir === 'up') {
                for (let y = 1; y < this.length; y++) moved = this.shift({ x, y }, 'up') || moved;
            } else {
                for (let y = this.length - 2; y >= 0; y--) moved = this.shift({ x, y }, 'down') || moved;
            }
        }
        return moved;
    }

    shiftHorizontal(dir) {
        let moved = false;
        for (let y = 0; y < this.length; y++) {
            if (dir === 'left') {
                for (let x = 1; x < this.length; x++) moved = this.shift({ x, y }, 'left') || moved;
            } else {
                for (let x = this.length - 2; x >= 0; x--) moved = this.shift({ x, y }, 'right') || moved;
            }
        }
        return moved;
    }

    isInsideBlock(pos) {
        return pos.x >= 0 && pos.y >= 0 && pos.x < this.length && pos.y < this.length;
    }

    shift(pos, dir) {
        let moved = false;
        const movingTile = this.gameBoard[pos.y * this.length + pos.x];
        if (movingTile === 0) return false;

        let set = false;
        let moveTo = pos;
        while (!set) {
            moveTo = utils.move(moveTo, dir);
            const moveToTile = this.gameBoard[moveTo.y * this.length + moveTo.x];

            if (!this.isInsideBlock(moveTo) || (moveToTile !== 0 && moveToTile !== movingTile) || !!this.mergedPos.find(p => p.x === moveTo.x && p.y === moveTo.y)) {
                const moveBack = utils.move(moveTo, utils.oppDirection(dir));
                if (!(moveBack.x === pos.x && moveBack.y === pos.y)) {
                    this.gameBoard[pos.y * this.length + pos.x] = 0;
                    this.gameBoard[moveBack.y * this.length + moveBack.x] = movingTile;
                    moved = true;
                }
                set = true;
            }
            else if (moveToTile === movingTile) {
                moved = true;
                this.gameBoard[moveTo.y * this.length + moveTo.x] += 1;
                this.score += Math.floor(Math.pow(2, this.gameBoard[moveTo.y * this.length + moveTo.x]));
                this.gameBoard[pos.y * this.length + pos.x] = 0;
                this.mergedPos.push(moveTo);
                set = true;
            }
        }

        return moved;
    }
}

// Store active games
const activeGames = new Map();

// Export as slash subcommand
module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('2048')
        .setDescription('Play the classic 2048 sliding puzzle game'),

    async execute(interaction) {
        // Check if user already has an active game
        if (activeGames.has(interaction.user.id)) {
            return interaction.reply({
                content: '❌ You already have an active 2048 game! Please finish it before starting a new one.',
                flags: 64
            });
        }

        const game = new TwoZeroFourEight({
            message: interaction,
            isSlashGame: true,
            timeoutTime: 120000,
            buttonStyle: ButtonStyle.Primary,
            embed: {
                title: '2048 Game',
                color: '#FFA500',
                footerEnabled: true,
                footer: { text: 'Combine tiles to reach 2048!' },
                timestamp: true
            }
        });

        // Store the game instance
        activeGames.set(interaction.user.id, game);

        game.on('gameOver', ({ result, player, score }) => {
            console.log(`2048 game ended for ${player.tag} with score ${score} (${result})`);
            // Remove the game from active games when it ends
            activeGames.delete(player.id);
        });

        await game.startGame();
    },

    async handleButtonInteraction(interaction) {
        // Get the active game for this user
        const game = activeGames.get(interaction.user.id);
        
        if (!game) {
            return interaction.reply({
                content: '❌ No active 2048 game found. Please start a new game with `/games 2048`.',
                flags: 64
            });
        }

        // Defer the update to prevent timeout
        await interaction.deferUpdate().catch(e => {});

        // Check if this is the correct player
        if (interaction.user.id !== game.message.author.id) {
            if (game.options.playerOnlyMessage) {
                return interaction.followUp({ 
                    content: utils.formatMessage(game.options, 'playerOnlyMessage'), 
                    flags: 64 
                });
            }
            return;
        }

        let moved = false;
        game.mergedPos = [];
        const direction = interaction.customId.split('_')[1];
        
        if (direction === 'up' || direction === 'down') moved = game.shiftVertical(direction);
        if (direction === 'left' || direction === 'right') moved = game.shiftHorizontal(direction);

        if (moved) game.placeRandomTile();
        
        // Check if game is over
        if (game.isGameOver()) {
            const result = game.gameBoard.includes(11); // 2048 = 2^11
            activeGames.delete(interaction.user.id);
            game.emit('gameOver', { result: (result ? 'win' : 'lose'), player: game.message.author, score: game.score });
            
            const embed = new EmbedBuilder()
                .setTitle(game.options.embed.title)
                .setColor(game.options.embed.color)
                .setImage('attachment://gameboard.png')
                .addFields({ name: 'Final Score', value: game.score.toString() })

            if (game.options.embed.timestamp) {
                embed.setTimestamp();
            }

            if (game.options.embed.footerEnabled) {
                embed.setFooter({
                    text: game.options.embed.footer.text,
                    iconURL: game.options.embed.footer.iconURL
                });
            }

            return interaction.editReply({ 
                embeds: [embed], 
                components: utils.disableButtons(interaction.message.components), 
                files: [await game.getBoardImage()], 
                attachments: [] 
            });
        }

        // Update the game board
        const embed = new EmbedBuilder()
            .setTitle(game.options.embed.title)
            .setColor(game.options.embed.color)
            .setImage('attachment://gameboard.png')
            .addFields({ name: 'Current Score', value: game.score.toString() })

        if (game.options.embed.fields) {
            for (const field of game.options.embed.fields) {
                const replacedValue = field.value
                    .replace('{player.tag}', game.message.author.tag)
                    .replace('{player.username}', game.message.author.username)
                    .replace('{player}', `<@!${game.message.author.id}>`)
                    .replace('{player.displayName}', game.message.author.displayName)
                    .replace('{score}', game.score.toString());

                embed.addFields({
                    name: field.name,
                    value: replacedValue,
                    inline: field.inline || false,
                });
            }
        }

        if (game.options.embed.timestamp) {
            embed.setTimestamp();
        }

        if (game.options.embed.footerEnabled) {
            embed.setFooter({
                text: game.options.embed.footer.text,
                iconURL: game.options.embed.footer.iconURL
            });
        }

        return interaction.editReply({ 
            embeds: [embed], 
            files: [await game.getBoardImage()], 
            attachments: [] 
        });
    }
};
