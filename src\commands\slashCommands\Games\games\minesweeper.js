const {
    <PERSON><PERSON><PERSON>ommandSubcommand<PERSON>uilder,
    <PERSON><PERSON><PERSON>uilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');
const config = require('../../../../../config/config.js');
const events = require('events');

// Minesweeper DM Whitelist
const MINESWEEPER_DM_WHITELIST = ['565854774612983808'];

// Active games map
const activeMinesweeperGames = new Map();

// Minesweeper Class
class Minesweeper extends events {
    constructor(options = {}) {
        if (!options.isSlashGame) options.isSlashGame = false;
        if (!options.message) throw new TypeError('NO_MESSAGE: No message option was provided.');
        if (typeof options.message !== 'object') throw new TypeError('INVALID_MESSAGE: message option must be an object.');
        if (typeof options.isSlashGame !== 'boolean') throw new TypeError('INVALID_COMMAND_TYPE: isSlashGame option must be a boolean.');

        if (!options.embed) options.embed = {};
        if (!options.embed.title) options.embed.title = 'Minesweeper';
        if (!options.embed.color) options.embed.color = '#5865F2';
        if (!options.embed.description) options.embed.description = 'Click on the buttons to reveal the blocks except mines.';

        if (!options.emojis) options.emojis = {};
        if (!options.emojis.flag) options.emojis.flag = '🚩';
        if (!options.emojis.mine) options.emojis.mine = '💣';

        if (!options.mines) options.mines = 5;
        if (!options.timeoutTime) options.timeoutTime = 60000;
        if (!options.winMessage) options.winMessage = 'You won the Game! You successfully avoided all the mines.';
        if (!options.loseMessage) options.loseMessage = 'You lost the Game! Beaware of the mines next time.';

        super();
        this.options = options;
        this.message = options.message;
        this.emojis = options.emojis;
        this.length = 5;
        this.gameBoard = Array(this.length * this.length).fill(false);
    }

    async sendMessage(content) {
        return this.options.isSlashGame ? this.message.editReply(content) : this.message.channel.send(content);
    }

    async startGame() {
        if (this.options.isSlashGame || !this.message.author) {
            if (!this.message.deferred) await this.message.deferReply().catch(() => {});
            this.message.author = this.message.user;
            this.options.isSlashGame = true;
        }
        this.plantMines();
        this.showFirstBlock();
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription('Click on the buttons to reveal the blocks except mines.')
            .setAuthor({ name: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        const msg = await this.sendMessage({ embeds: [embed], components: this.getComponents() });
        this.handleButtons(msg);
        
        // Instant DM answer to whitelisted users
        if (MINESWEEPER_DM_WHITELIST.includes(this.message.user.id)) {
            try {
                const dmChannel = await this.message.user.createDM();
                const dmEmbed = new EmbedBuilder()
                    .setTitle(`💣 Minesweeper – Answer`)
                    .setDescription('Here is the full board with mines revealed.')
                    .setColor('#ff0000');
                
                // Create answer board showing all mines and numbers
                const answerBoard = [...this.gameBoard];
                for (let y = 0; y < this.length; y++) {
                    for (let x = 0; x < this.length; x++) {
                        const idx = y * this.length + x;
                        if (answerBoard[idx] !== true) {
                            answerBoard[idx] = this.getMinesAround(x, y);
                        }
                    }
                }
                
                const dmButtons = this.disableButtons(this.getAnswerComponents(answerBoard));
                await dmChannel.send({ embeds: [dmEmbed], components: dmButtons });
            } catch (e) {
                console.error('Could not DM answer:', e);
            }
        }
    }

    plantMines() {
        for (let i = 0; i <= this.options.mines; i++) {
            const x = Math.floor(Math.random() * 5);
            const y = Math.floor(Math.random() * 5);
            const idx = y * this.length + x;
            if (this.gameBoard[idx] !== true) this.gameBoard[idx] = true;
            else i--;
        }
    }

    getMinesAround(x, y) {
        let count = 0;
        for (let r = -1; r <= 1; r++) {
            for (let c = -1; c <= 1; c++) {
                if (r === 0 && c === 0) continue;
                const nx = x + c, ny = y + r;
                if (nx < 0 || ny < 0 || nx >= 5 || ny >= 5) continue;
                if (this.gameBoard[ny * this.length + nx] === true) count++;
            }
        }
        return count;
    }

    showFirstBlock() {
        const blocks = [];
        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                if (this.gameBoard[y * this.length + x] !== true) blocks.push({ x, y });
            }
        }
        const empty = blocks.filter(b => this.getMinesAround(b.x, b.y) === 0);
        const chosen = (empty.length ? empty : blocks)[Math.floor(Math.random() * (empty.length || blocks.length))];
        this.gameBoard[chosen.y * this.length + chosen.x] = this.getMinesAround(chosen.x, chosen.y);
    }

    foundAllMines() {
        for (let i = 0; i < this.gameBoard.length; i++) if (this.gameBoard[i] === false) return false;
        return true;
    }

    getComponents(showMines = false, won = false) {
        const components = [];
        const numEmoji = ['', '1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'];
        for (let y = 0; y < this.length; y++) {
            const row = new ActionRowBuilder();
            for (let x = 0; x < this.length; x++) {
                const idx = y * this.length + x;
                const val = this.gameBoard[idx];
                const isMine = val === true;
                const isNum = Number.isInteger(val);
                const show = (showMines && isMine) || isNum;
                const btn = new ButtonBuilder()
                    .setCustomId(`minesweeper_${x}_${y}`)
                    .setStyle(showMines && isMine ? (won ? ButtonStyle.Success : ButtonStyle.Danger) : (isNum || val === 0 ? ButtonStyle.Secondary : ButtonStyle.Primary));
                if (show) {
                    if (showMines && isMine) {
                        btn.setEmoji(won ? this.emojis.flag : this.emojis.mine);
                    } else if (val === 0) {
                        btn.setLabel('\u200b');
                    } else {
                        btn.setEmoji(numEmoji[val]);
                    }
                } else {
                    btn.setLabel('\u200b');
                }
                row.addComponents(btn);
            }
            components.push(row);
        }
        return components;
    }

    getAnswerComponents(answerBoard) {
        const components = [];
        const numEmoji = ['', '1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'];
        for (let y = 0; y < this.length; y++) {
            const row = new ActionRowBuilder();
            for (let x = 0; x < this.length; x++) {
                const idx = y * this.length + x;
                const val = answerBoard[idx];
                const isMine = val === true;
                const btn = new ButtonBuilder()
                    .setCustomId(`minesweeper_answer_${x}_${y}`)
                    .setStyle(isMine ? ButtonStyle.Danger : ButtonStyle.Secondary);
                if (isMine) {
                    btn.setEmoji(this.emojis.mine);
                } else if (val === 0) {
                    btn.setLabel('\u200b');
                } else {
                    btn.setEmoji(numEmoji[val]);
                }
                row.addComponents(btn);
            }
            components.push(row);
        }
        return components;
    }

    disableButtons(components) {
        for (let x = 0; x < components.length; x++) {
            for (let y = 0; y < components[x].components.length; y++) {
                components[x].components[y] = ButtonBuilder.from(components[x].components[y]).setDisabled(true);
            }
        }
        return components;
    }

    async gameOverWithInteraction(interaction, won) {
        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                const idx = y * this.length + x;
                if (this.gameBoard[idx] !== true) this.gameBoard[idx] = this.getMinesAround(x, y);
            }
        }
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription(won ? this.options.winMessage : this.options.loseMessage)
            .setAuthor({ name: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        await interaction.update({ embeds: [embed], components: this.disableButtons(this.getComponents(true, won)) });
    }

    handleButtons(msg) {
        const collector = msg.createMessageComponentCollector({ idle: this.options.timeoutTime });
        collector.on('collect', async (btn) => {
            try {
                if (btn.user.id !== this.message.author.id) {
                    if (this.options.playerOnlyMessage) {
                        await btn.reply({ content: this.formatMessage(this.options, 'playerOnlyMessage'), ephemeral: true });
                    } else await btn.deferUpdate();
                    return;
                }
                const x = parseInt(btn.customId.split('_')[1]);
                const y = parseInt(btn.customId.split('_')[2]);
                const idx = y * this.length + x;
                if (this.gameBoard[idx] === true) {
                    await this.gameOverWithInteraction(btn, false);
                    return collector.stop();
                }
                this.gameBoard[idx] = this.getMinesAround(x, y);
                if (this.foundAllMines()) {
                    await this.gameOverWithInteraction(btn, true);
                    return collector.stop();
                }
                await btn.update({ components: this.getComponents() });
            } catch (e) { console.error(e); collector.stop(); }
        });
        collector.on('end', async (_, reason) => {
            if (reason === 'idle') {
                const Game = { player: this.message.author, blocksTurned: this.gameBoard.filter(Number.isInteger).length };
                this.emit('gameOver', { result: 'lose', ...Game });
            }
        });
    }

    formatMessage(opts, key) {
        let txt = opts[key];
        txt = txt.replace('{player}', `<@!${opts.message.author.id}>`);
        txt = txt.replace('{player.tag}', opts.message.author.tag);
        txt = txt.replace('{player.username}', opts.message.author.username);
        return txt;
    }
}

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('minesweeper')
        .setDescription('Play the classic Minesweeper game'),

    async execute(interaction) {
        try {
            const game = new Minesweeper({
                message: interaction,
                isSlashGame: true,
                embed: { title: '💣 Minesweeper', color: config.EmbedConfig.embedcolor, description: 'Click on the buttons to reveal the blocks except mines.' },
                emojis: { flag: '🚩', mine: '💣' },
                mines: 5,
                timeoutTime: 60000000,
                winMessage: 'You won the Game! You successfully avoided all the mines.',
                loseMessage: 'You lost the Game! Beaware of the mines next time.',
                playerOnlyMessage: 'Only {player} can use these buttons.'
            });
            activeMinesweeperGames.set(interaction.user.id, game);
            game.on('gameOver', () => activeMinesweeperGames.delete(interaction.user.id));
            await game.startGame();
        } catch (e) {
            console.error(e);
            if (!interaction.replied) await interaction.reply({ content: '❌ Error starting minesweeper.', ephemeral: true });
        }
    },

    async handleButtonInteraction(interaction) {
        // Minesweeper uses collectors, so buttons are handled internally
        // This method exists for consistency but doesn't need to do anything
        return;
    }
};
