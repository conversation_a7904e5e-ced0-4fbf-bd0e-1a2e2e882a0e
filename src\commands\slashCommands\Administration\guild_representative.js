const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const GuildRepresentative = require('../../../models/GuildRepresentative');

// Constants
const MAIN_SERVER_ID = '1362356687092191442';
const GUILD_REP_CHANNEL_ID = '1390960754311692379';
const GUILD_REP_ROLE_ID = '1390995985638883338';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('guild_representative')
        .setDescription('Manage guild representatives')
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a guild representative')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('The user to set as guild representative')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('guildid')
                        .setDescription('The guild ID they represent')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a guild representative')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('The user to remove as guild representative')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('guildid')
                        .setDescription('The guild ID to remove them from (optional)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all guild representatives')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Show representatives for a specific user (optional)')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('guildid')
                        .setDescription('Show representatives for a specific guild (optional)')
                        .setRequired(false)))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    name: 'guild_representative',
    category: 'admin',
    aliases: [],
    cooldown: 5,
    usage: '/guild_representative add user:<user> guildid:<guild_id> | /guild_representative remove user:<user> [guildid:<guild_id>] | /guild_representative list [user:<user>] [guildid:<guild_id>]',
    description: 'Manage guild representatives - add, remove, or list them',
    memberpermissions: [PermissionFlagsBits.Administrator],
    botpermissions: ['ManageRoles', 'ManageMessages'],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'guild', // This admin command should only work in guilds

    async execute(interaction) {
        await interaction.deferReply();

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'add':
                    await this.handleAddSubcommand(interaction);
                    break;
                case 'remove':
                    await this.handleRemoveSubcommand(interaction);
                    break;
                case 'list':
                    await this.handleListSubcommand(interaction);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                    });
            }
        } catch (error) {
            console.error('Error in guild_representative command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing the command. Please try again.')
                .setColor('#e74c3c')
                .setTimestamp();

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    async handleAddSubcommand(interaction) {
        const user = interaction.options.getUser('user');
        const guildId = interaction.options.getString('guildid');

        // Validate guild ID format
        if (!/^\d{17,19}$/.test(guildId)) {
            return await interaction.editReply({
                content: '❌ Invalid guild ID format. Please provide a valid Discord server ID.',
            });
        }

        // Check if user is in the main server
        const mainGuild = interaction.client.guilds.cache.get(MAIN_SERVER_ID);
        if (!mainGuild) {
            return await interaction.editReply({
                content: '❌ Cannot access the main RankBreaker server.',
            });
        }

        const member = await mainGuild.members.fetch(user.id).catch(() => null);
        if (!member) {
            return await interaction.editReply({
                content: `❌ ${user.tag} is not a member of the main RankBreaker server.`,
            });
        }

        // Check if the bot is in the target guild
        const targetGuild = interaction.client.guilds.cache.get(guildId);
        if (!targetGuild) {
            // Bot is not in the guild, remove any existing representative entry and clean up
            await this.cleanupRepresentative(interaction.client, user.id, guildId);
            
            return await interaction.editReply({
                content: `❌ Bot is not in the specified guild (ID: ${guildId}). Any existing representative data has been cleaned up.`,
            });
        }

        // Check if representative already exists
        const existingRep = await GuildRepresentative.findRepresentative(user.id, guildId);
        if (existingRep) {
            return await interaction.editReply({
                content: `❌ ${user.tag} is already a representative for guild **${targetGuild.name}**.`,
            });
        }

        // Create new guild representative entry
        const newRepresentative = new GuildRepresentative({
            userId: user.id,
            guildId: guildId,
            addedBy: interaction.user.id
        });

        await newRepresentative.save();

        // Add the Guild Representative role
        const guildRepRole = mainGuild.roles.cache.get(GUILD_REP_ROLE_ID);
        if (guildRepRole && !member.roles.cache.has(GUILD_REP_ROLE_ID)) {
            await member.roles.add(guildRepRole, `Added as guild representative for ${targetGuild.name} by ${interaction.user.tag}`);
        }

        // Create success embed
        const successEmbed = new EmbedBuilder()
            .setTitle('✅ Guild Representative Added')
            .setDescription(`Successfully set **${user.tag}** as a guild representative.`)
            .addFields(
                { name: '👤 User', value: `${user} (${user.tag})`, inline: true },
                { name: '🏰 Guild', value: `**${targetGuild.name}**\n\`${guildId}\``, inline: true },
                { name: '👑 Added By', value: `${interaction.user}`, inline: true }
            )
            .setColor('#2ecc71')
            .setTimestamp()
            .setThumbnail(user.displayAvatarURL({ dynamic: true }));

        if (targetGuild.iconURL()) {
            successEmbed.setFooter({ 
                text: targetGuild.name, 
                iconURL: targetGuild.iconURL({ dynamic: true }) 
            });
        }

        await interaction.editReply({ embeds: [successEmbed] });
    },

    async handleRemoveSubcommand(interaction) {
        const user = interaction.options.getUser('user');
        const guildId = interaction.options.getString('guildid');

        if (guildId) {
            // Remove from specific guild
            if (!/^\d{17,19}$/.test(guildId)) {
                return await interaction.editReply({
                    content: '❌ Invalid guild ID format. Please provide a valid Discord server ID.',
                });
            }

            const existingRep = await GuildRepresentative.findRepresentative(user.id, guildId);
            if (!existingRep) {
                return await interaction.editReply({
                    content: `❌ ${user.tag} is not a representative for the specified guild.`,
                });
            }

            await this.cleanupRepresentative(interaction.client, user.id, guildId);

            const targetGuild = interaction.client.guilds.cache.get(guildId);
            const guildName = targetGuild ? targetGuild.name : `Guild ID: ${guildId}`;

            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Guild Representative Removed')
                .setDescription(`Successfully removed **${user.tag}** as a guild representative.`)
                .addFields(
                    { name: '👤 User', value: `${user} (${user.tag})`, inline: true },
                    { name: '🏰 Guild', value: `**${guildName}**\n\`${guildId}\``, inline: true },
                    { name: '👑 Removed By', value: `${interaction.user}`, inline: true }
                )
                .setColor('#e74c3c')
                .setTimestamp()
                .setThumbnail(user.displayAvatarURL({ dynamic: true }));

            await interaction.editReply({ embeds: [successEmbed] });

        } else {
            // Remove from all guilds
            const representatives = await GuildRepresentative.findByUser(user.id);
            if (representatives.length === 0) {
                return await interaction.editReply({
                    content: `❌ ${user.tag} is not a guild representative for any guilds.`,
                });
            }

            // Remove all representative entries
            await GuildRepresentative.removeAllByUser(user.id);

            // Remove role and delete messages
            const mainGuild = interaction.client.guilds.cache.get(MAIN_SERVER_ID);
            if (mainGuild) {
                const member = await mainGuild.members.fetch(user.id).catch(() => null);
                if (member) {
                    const guildRepRole = mainGuild.roles.cache.get(GUILD_REP_ROLE_ID);
                    if (guildRepRole && member.roles.cache.has(GUILD_REP_ROLE_ID)) {
                        await member.roles.remove(guildRepRole, `Removed from all guild representative roles by ${interaction.user.tag}`);
                    }
                }
            }

            await this.deleteUserMessages(interaction.client, user.id);

            const successEmbed = new EmbedBuilder()
                .setTitle('✅ All Guild Representative Roles Removed')
                .setDescription(`Successfully removed **${user.tag}** from all guild representative roles.`)
                .addFields(
                    { name: '👤 User', value: `${user} (${user.tag})`, inline: true },
                    { name: '🏰 Guilds Removed', value: `**${representatives.length}** guild(s)`, inline: true },
                    { name: '👑 Removed By', value: `${interaction.user}`, inline: true }
                )
                .setColor('#e74c3c')
                .setTimestamp()
                .setThumbnail(user.displayAvatarURL({ dynamic: true }));

            await interaction.editReply({ embeds: [successEmbed] });
        }
    },

    async handleListSubcommand(interaction) {
        const user = interaction.options.getUser('user');
        const guildId = interaction.options.getString('guildid');

        let representatives = [];
        let title = '';
        let description = '';

        if (user && guildId) {
            // Check specific user for specific guild
            if (!/^\d{17,19}$/.test(guildId)) {
                return await interaction.editReply({
                    content: '❌ Invalid guild ID format. Please provide a valid Discord server ID.',
                });
            }

            const rep = await GuildRepresentative.findRepresentative(user.id, guildId);
            if (rep) {
                representatives = [rep];
                title = `Guild Representative Status`;
                description = `Checking if **${user.tag}** represents the specified guild.`;
            } else {
                return await interaction.editReply({
                    content: `❌ ${user.tag} is not a representative for the specified guild.`,
                });
            }

        } else if (user) {
            // Show all guilds for specific user
            representatives = await GuildRepresentative.findByUser(user.id);
            title = `Guild Representatives for ${user.tag}`;
            description = `Showing all guilds that **${user.tag}** represents.`;

        } else if (guildId) {
            // Show all users for specific guild
            if (!/^\d{17,19}$/.test(guildId)) {
                return await interaction.editReply({
                    content: '❌ Invalid guild ID format. Please provide a valid Discord server ID.',
                });
            }

            representatives = await GuildRepresentative.findByGuild(guildId);
            const targetGuild = interaction.client.guilds.cache.get(guildId);
            const guildName = targetGuild ? targetGuild.name : `Guild ID: ${guildId}`;
            title = `Guild Representatives for ${guildName}`;
            description = `Showing all representatives for **${guildName}**.`;

        } else {
            // Show all representatives
            representatives = await GuildRepresentative.find({}).sort({ addedAt: -1 }).limit(25);
            title = 'All Guild Representatives';
            description = 'Showing all guild representatives (limited to 25 most recent).';
        }

        if (representatives.length === 0) {
            return await interaction.editReply({
                content: '❌ No guild representatives found matching your criteria.',
            });
        }

        const embed = new EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor('#3498db')
            .setTimestamp();

        // Add fields for each representative
        for (const rep of representatives.slice(0, 25)) {
            const repUser = await interaction.client.users.fetch(rep.userId).catch(() => null);
            const repGuild = interaction.client.guilds.cache.get(rep.guildId);
            const addedByUser = await interaction.client.users.fetch(rep.addedBy).catch(() => null);

            const userName = repUser ? repUser.tag : `User ID: ${rep.userId}`;
            const guildName = repGuild ? repGuild.name : `Guild ID: ${rep.guildId}`;
            const addedByName = addedByUser ? addedByUser.tag : `User ID: ${rep.addedBy}`;

            embed.addFields({
                name: `👤 ${userName}`,
                value: `🏰 **Guild:** ${guildName}\n📅 **Added:** <t:${Math.floor(rep.addedAt.getTime() / 1000)}:R>\n👑 **Added By:** ${addedByName}`,
                inline: true
            });
        }

        if (representatives.length > 25) {
            embed.setFooter({ text: `Showing 25 of ${representatives.length} representatives` });
        }

        await interaction.editReply({ embeds: [embed] });
    },

    async cleanupRepresentative(client, userId, guildId) {
        try {
            // Remove from database
            await GuildRepresentative.removeRepresentative(userId, guildId);

            // Get main server and member
            const mainGuild = client.guilds.cache.get(MAIN_SERVER_ID);
            if (!mainGuild) return;

            const member = await mainGuild.members.fetch(userId).catch(() => null);
            if (!member) return;

            // Check if user has any other representative roles
            const otherReps = await GuildRepresentative.findByUser(userId);
            
            // If no other representative roles, remove the Guild Representative role
            if (otherReps.length === 0) {
                const guildRepRole = mainGuild.roles.cache.get(GUILD_REP_ROLE_ID);
                if (guildRepRole && member.roles.cache.has(GUILD_REP_ROLE_ID)) {
                    await member.roles.remove(guildRepRole, 'No longer a guild representative');
                }
            }

            // Delete messages from the guild rep channel
            await this.deleteUserMessages(client, userId);

        } catch (error) {
            console.error('Error cleaning up representative:', error);
        }
    },

    async deleteUserMessages(client, userId) {
        try {
            const mainGuild = client.guilds.cache.get(MAIN_SERVER_ID);
            if (!mainGuild) return;

            const channel = mainGuild.channels.cache.get(GUILD_REP_CHANNEL_ID);
            if (!channel || !channel.isTextBased()) return;

            // Fetch messages and delete those from the user
            let deletedCount = 0;
            let lastMessageId = null;

            while (true) {
                const options = { limit: 100 };
                if (lastMessageId) {
                    options.before = lastMessageId;
                }

                const messages = await channel.messages.fetch(options);
                if (messages.size === 0) break;

                const userMessages = messages.filter(msg => msg.author.id === userId);
                
                if (userMessages.size === 0) {
                    lastMessageId = messages.last().id;
                    continue;
                }

                // Delete messages in batches
                for (const message of userMessages.values()) {
                    try {
                        await message.delete();
                        deletedCount++;
                        // Small delay to avoid rate limits
                        await new Promise(resolve => setTimeout(resolve, 100));
                    } catch (error) {
                        console.error(`Failed to delete message ${message.id}:`, error);
                    }
                }

                lastMessageId = messages.last().id;

                // If we fetched less than 100 messages, we've reached the end
                if (messages.size < 100) break;
            }

            console.log(`Deleted ${deletedCount} messages from user ${userId} in guild rep channel`);

        } catch (error) {
            console.error('Error deleting user messages:', error);
        }
    }
};