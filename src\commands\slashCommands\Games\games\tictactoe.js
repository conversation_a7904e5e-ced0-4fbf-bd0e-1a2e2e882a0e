const {
    <PERSON><PERSON><PERSON><PERSON>mandSubcommand<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    Button<PERSON><PERSON>er,
    ButtonStyle,
    ComponentType,
    Colors
} = require('discord.js');

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('tictactoe')
        .setDescription('Play Tic-Tac-Toe')
        .addUserOption(opt => opt.setName('opponent').setDescription('Opponent (optional)'))
        .addStringOption(opt => opt.setName('option').setDescription('Board size').addChoices(
            { name: '3x3 (Classic)', value: '3x3' },
            { name: '4x4 (Extended)', value: '4x4' }
        )),

    async execute(interaction) {
        const opponent = interaction.options.getUser('opponent');
        const gameOption = interaction.options.getString('option') || '3x3';
        const is4x4 = gameOption === '4x4';
        const boardSize = is4x4 ? 16 : 9;
        const board = new Array(boardSize).fill(' ');

        let currentPlayer = interaction.user;
        let otherPlayer = opponent || 'bot';
        let buttons = this.createBoardButtons(board, is4x4);

        if (opponent) {
            if (opponent.id === interaction.user.id)
                return interaction.reply({ content: 'You cannot play against yourself!', ephemeral: true });

            const confirmEmbed = new EmbedBuilder()
                .setColor(Colors.Green)
                .setTitle(`Tic-Tac-Toe Challenge ${is4x4 ? '(4x4)' : '(3x3)'}`)
                .setDescription(`${interaction.user} has challenged ${opponent} to a game of Tic-Tac-Toe!`)
                .setFooter({ text: 'Accept the challenge to start the game' });

            const acceptBtn = new ButtonBuilder().setCustomId('ttt_accept').setLabel('Accept').setStyle(ButtonStyle.Success);
            const declineBtn = new ButtonBuilder().setCustomId('ttt_decline').setLabel('Decline').setStyle(ButtonStyle.Danger);
            await interaction.reply({ content: `${opponent}`, embeds: [confirmEmbed], components: [new ActionRowBuilder().addComponents(acceptBtn, declineBtn)] });

            const filter = i => i.user.id === opponent.id;
            const collector = interaction.channel.createMessageComponentCollector({ filter, time: 60000 });
            collector.on('collect', async i => {
                if (i.customId === 'ttt_accept') {
                    await i.update({ content: `Game on! ${interaction.user} vs ${opponent} ${is4x4 ? '(4x4)' : '(3x3)'}`, components: [] });
                    this.startTicTacToeGame(interaction, board, currentPlayer, opponent, buttons, is4x4);
                } else {
                    await i.update({ content: `${opponent} declined the challenge.`, components: [] });
                }
            });
            collector.on('end', () => interaction.editReply({ content: 'Challenge timed out.', components: [] }).catch(() => {}));
        } else {
            await interaction.reply({ content: `${currentPlayer}'s turn (X) ${is4x4 ? '- 4x4 Mode' : '- 3x3 Mode'}`, components: buttons });
            const gameState = { board, currentPlayer, player1: currentPlayer, player2: otherPlayer, isGameOver: false, isVsBot: true, isDM: !interaction.guild, interaction, gameMessage: await interaction.fetchReply(), is4x4 };
            this.setupGameCollector(gameState);
        }
    },

    async startTicTacToeGame(interaction, board, player1, player2, buttons, is4x4 = false) {
        let currentPlayer = player1, isGameOver = false;
        const isVsBot = player2 === 'bot', isDM = !interaction.guild;
        const gameMessage = await interaction.followUp({ content: `${currentPlayer}'s turn (X) ${is4x4 ? '- 4x4 Mode' : '- 3x3 Mode'}`, components: buttons });
        const gameState = { board, currentPlayer, player1, player2, isGameOver, isVsBot, isDM, interaction, gameMessage, is4x4 };
        this.setupGameCollector(gameState);
    },

    setupGameCollector(gameState) {
        const { gameMessage, interaction } = gameState;
        const collector = gameMessage.createMessageComponentCollector({ componentType: ComponentType.Button, time: 300_000 });
        collector.on('collect', i => this.handleGameMove(i, gameState, collector));
        collector.on('end', () => { if (!gameState.isGameOver) gameMessage.edit({ content: '⏰ Game timed out.', components: [] }).catch(() => {}); });
    },

    async handleGameMove(i, gameState, collector) {
        const { board, currentPlayer, player1, player2, isVsBot } = gameState;
        if ((isVsBot && i.user.id !== player1.id) || (!isVsBot && i.user.id !== currentPlayer.id))
            return i.reply({ content: "It's not your turn!", ephemeral: true });

        const pos = parseInt(i.customId.replace('ttt_', ''));
        if (board[pos] !== ' ') return i.reply({ content: 'Position taken!', ephemeral: true });

        board[pos] = currentPlayer === player1 ? 'X' : 'O';
        const win = this.checkWin(board, gameState.is4x4);
        const btns = this.createBoardButtons(board, gameState.is4x4);

        if (win) {
            gameState.isGameOver = true; collector.stop();
            return i.update({ content: `${currentPlayer} wins!`, components: btns });
        }
        if (!board.includes(' ')) {
            gameState.isGameOver = true; collector.stop();
            return i.update({ content: "It's a draw!", components: btns });
        }

        gameState.currentPlayer = currentPlayer === player1 ? player2 : player1;

        if (isVsBot && gameState.currentPlayer === 'bot') {
            await i.update({ content: '🤖 Bot is thinking...', components: btns });
            const botMove = this.getBotMove(board, gameState.is4x4);
            if (botMove !== -1) board[botMove] = 'O';
            const botWin = this.checkWin(board, gameState.is4x4);
            const newBtns = this.createBoardButtons(board, gameState.is4x4);
            if (botWin) {
                gameState.isGameOver = true; collector.stop();
                return i.editReply({ content: '🤖 Bot wins!', components: newBtns });
            }
            if (!board.includes(' ')) {
                gameState.isGameOver = true; collector.stop();
                return i.editReply({ content: "It's a draw!", components: newBtns });
            }
            gameState.currentPlayer = player1;
            return i.editReply({ content: `${player1}'s turn (X)`, components: newBtns });
        } else {
            return i.update({ content: `${gameState.currentPlayer}'s turn (${gameState.currentPlayer === player1 ? 'X' : 'O'})`, components: btns });
        }
    },

    async handleButtonInteraction(interaction) {
        // This will be handled by the collector in setupGameCollector
        // But we need this method for the main dispatcher
        return;
    },

    createBoardButtons(board, is4x4 = false) {
        const buttons = [];
        const size = is4x4 ? 4 : 3;
        for (let i = 0; i < size; i++) {
            const row = new ActionRowBuilder();
            for (let j = 0; j < size; j++) {
                const pos = i * size + j;
                const label = board[pos] === ' ' ? '\u200B' : board[pos];
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`ttt_${pos}`)
                        .setLabel(label)
                        .setStyle(board[pos] === 'X' ? ButtonStyle.Primary : board[pos] === 'O' ? ButtonStyle.Danger : ButtonStyle.Secondary)
                        .setDisabled(board[pos] !== ' ')
                );
            }
            buttons.push(row);
        }
        return buttons;
    },

    checkWin(board, is4x4 = false) {
        const size = is4x4 ? 4 : 3;
        const winLen = is4x4 ? 4 : 3;
        const patterns = [];
        
        // horizontal
        for (let r = 0; r < size; r++)
            for (let c = 0; c <= size - winLen; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push(r * size + c + i);
                patterns.push(p);
            }
        
        // vertical
        for (let c = 0; c < size; c++)
            for (let r = 0; r <= size - winLen; r++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + c);
                patterns.push(p);
            }
        
        // diagonal ↘
        for (let r = 0; r <= size - winLen; r++)
            for (let c = 0; c <= size - winLen; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + (c + i));
                patterns.push(p);
            }
        
        // diagonal ↙
        for (let r = 0; r <= size - winLen; r++)
            for (let c = winLen - 1; c < size; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + (c - i));
                patterns.push(p);
            }

        for (const p of patterns) {
            const first = board[p[0]];
            if (first !== ' ' && p.every(idx => board[idx] === first)) return true;
        }
        return false;
    },

    getBotMove(board, is4x4 = false) {
        if (is4x4) return this.getBotMove4x4(board);
        return this.minimax(board, 0, true).index;
    },

    getBotMove4x4(board) {
        // 1. Try to win immediately
        const winMove = this.findWinningMove(board, 'O', true);
        if (winMove !== -1) return winMove;

        // 2. Block player from winning
        const blockMove = this.findWinningMove(board, 'X', true);
        if (blockMove !== -1) return blockMove;

        // 3. Fallback to first available spot
        for (let i = 0; i < board.length; i++) if (board[i] === ' ') return i;
        return -1;
    },

    findWinningMove(board, player, is4x4 = false) {
        for (let i = 0; i < board.length; i++) {
            if (board[i] === ' ') {
                board[i] = player;
                if (this.checkWin(board, is4x4)) {
                    board[i] = ' '; // undo
                    return i;
                }
                board[i] = ' ';
            }
        }
        return -1;
    },

    minimax(board, depth, isMax) {
        const winner = this.evaluateBoard(board);
        if (winner === 'O') return { score: 10 - depth };
        if (winner === 'X') return { score: depth - 10 };
        if (!board.includes(' ')) return { score: 0 };

        let best = isMax ? { score: -Infinity } : { score: Infinity };
        for (let i = 0; i < board.length; i++) {
            if (board[i] !== ' ') continue;
            board[i] = isMax ? 'O' : 'X';
            const val = this.minimax(board, depth + 1, !isMax);
            board[i] = ' ';
            if (isMax ? val.score > best.score : val.score < best.score) best = { score: val.score, index: i };
        }
        return best;
    },

    evaluateBoard(board) {
        const size = board.length === 16 ? 4 : 3;
        const winLen = size === 4 ? 4 : 3;
        const patterns = [];
        
        // horizontal
        for (let r = 0; r < size; r++)
            for (let c = 0; c <= size - winLen; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push(r * size + c + i);
                patterns.push(p);
            }
        
        // vertical
        for (let c = 0; c < size; c++)
            for (let r = 0; r <= size - winLen; r++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + c);
                patterns.push(p);
            }
        
        // diagonal ↘
        for (let r = 0; r <= size - winLen; r++)
            for (let c = 0; c <= size - winLen; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + (c + i));
                patterns.push(p);
            }
        
        // diagonal ↙
        for (let r = 0; r <= size - winLen; r++)
            for (let c = winLen - 1; c < size; c++) {
                const p = [];
                for (let i = 0; i < winLen; i++) p.push((r + i) * size + (c - i));
                patterns.push(p);
            }

        for (const p of patterns) {
            const first = board[p[0]];
            if (first !== ' ' && p.every(idx => board[idx] === first)) return first;
        }
        return null;
    }
};
