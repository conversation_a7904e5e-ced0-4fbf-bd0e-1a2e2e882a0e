const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('glist')
        .setDescription('List all active giveaways'),
    name: 'glist',
    category: 'info',
    aliases: ['giveawaylist', 'listgiveaways'],
    cooldown: 10,
    usage: '/glist',
    description: 'List all active giveaways',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(interaction) {
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Check if giveaway file exists
        if (!fs.existsSync(giveawayFile)) {
            return interaction.reply({ content: '❌ No giveaways found!', ephemeral: true });
        }

        let giveaways = {};
        try {
            giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
        } catch (error) {
            return interaction.reply({ content: '❌ Error reading giveaway data!', ephemeral: true });
        }

        // Filter active giveaways
        const activeGiveaways = Object.entries(giveaways).filter(([id, giveaway]) => giveaway.active);

        if (activeGiveaways.length === 0) {
            return interaction.reply({ content: '❌ No active giveaways found!', ephemeral: true });
        }

        // Create embed
        const embed = new EmbedBuilder()
            .setTitle('🎉 Active Giveaways')
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: 'RankBreaker Giveaways', iconURL: interaction.client.user.displayAvatarURL() });

        let description = '';
        for (const [giveawayId, giveaway] of activeGiveaways) {
            const startTime = new Date(giveaway.startTime);
            const timeAgo = Math.floor((Date.now() - giveaway.startTime) / 1000 / 60); // minutes ago
            
            let host;
            try {
                host = await interaction.client.users.fetch(giveaway.host);
            } catch (error) {
                host = { username: 'Unknown User' };
            }

            description += `**${giveawayId}**\n`;
            description += `📦 **Prize:** ${giveaway.prize}\n`;
            description += `👤 **Host:** ${host.username}\n`;
            description += `📊 **Entries:** ${giveaway.entries.length}\n`;
            description += `⏰ **Started:** ${timeAgo} minutes ago\n`;
            description += `📍 **Channel:** <#${giveaway.channelId}>\n\n`;
        }

        embed.setDescription(description);

        return interaction.reply({ embeds: [embed], ephemeral: true });
    },
};
