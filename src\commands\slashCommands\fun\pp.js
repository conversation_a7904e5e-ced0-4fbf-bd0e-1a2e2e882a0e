const { SlashCommandBuilder } = require('discord.js');
const config = require('../../../../config/config.js');
const axios = require('axios');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('pp')
        .setDescription('Shows the pp size of a user.')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to check the pp size of.')
                .setRequired(false)
        ),
    name: 'pp',
    category: 'Fun',
    aliases: [],
    cooldown: 10,
    usage: '[user]',
    description: 'Shows the pp size of a user.',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 1,
    nsfw: false,
    BotOwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both',

    // Text normalizer function to clean up display names
    normalizeText(text) {
        // Remove markdown formatting (**, __, etc.)
        let normalized = text.replace(/\*\*/g, '').replace(/__/g, '').replace(/\*/g, '').replace(/_/g, '');
        
        // Convert Unicode fancy text to normal characters
        normalized = normalized.normalize('NFD').replace(/[\u0300-\u036f]/g, ''); // Remove diacritics
        
        // Map fancy Unicode characters to normal ones
        const unicodeMap = {
            // Mathematical Script/Bold characters
            '𝓢': 'S', '𝓪': 'a', '𝓽': 't', '𝓮': 'e', '𝓴': 'k', '𝓵': 'l',
            '𝒮': 'S', '𝒶': 'a', '𝓉': 't', '𝑒': 'e', '𝓀': 'k', '𝓁': 'l',
            '𝕊': 'S', '𝕒': 'a', '𝕥': 't', '𝕖': 'e', '𝕜': 'k', '𝕝': 'l',
            '𝖲': 'S', '𝖺': 'a', '𝗍': 't', '𝖾': 'e', '𝗄': 'k', '𝗅': 'l',
            '𝘚': 'S', '𝘢': 'a', '𝘵': 't', '𝘦': 'e', '𝘬': 'k', '𝘭': 'l',
            '𝙎': 'S', '𝙖': 'a', '𝙩': 't', '𝙚': 'e', '𝙠': 'k', '𝙡': 'l'
        };
        
        // Replace Unicode characters
        for (const [unicode, normal] of Object.entries(unicodeMap)) {
            normalized = normalized.replace(new RegExp(unicode, 'g'), normal);
        }
        
        // Specific name mappings
        const nameMap = {
            'satekka': 'satella',
            'Satekka': 'Satella',
            'SATEKKA': 'SATELLA'
        };
        
        // Apply specific name mappings
        for (const [original, replacement] of Object.entries(nameMap)) {
            normalized = normalized.replace(new RegExp(original, 'g'), replacement);
        }
        
        // Replace numbers with letters (leet speak conversion)
        const leetMap = {
            '0': 'o',
            '1': 'i',
            '3': 'e',
            '4': 'a',
            '5': 's',
            '7': 't',
            '8': 'b'
        };
        
        normalized = normalized.replace(/[01345678]/g, (match) => leetMap[match] || match);
        
        // Replace underscores and numbers with spaces and capitalize
        normalized = normalized.replace(/[_\d]+/g, ' ');
        
        // Clean up multiple spaces
        normalized = normalized.replace(/\s+/g, ' ').trim();
        
        // Capitalize each word
        normalized = normalized.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
        
        return normalized;
    },

    async execute(interaction) {

        const specialUserIds = ['565854774612983808', '913390209352212481']; //place here ids of user you want pp big
        const smallUsersIds = ['1231217991929036842', '900433233055985684', '712907863328161842']; //place here ids of users you want pp small (shortest length)

        const user = interaction.options.getUser('user') || interaction.user;

        // Handle both guild and user app contexts
        let displayName;
        let member = null;

        if (interaction.guild) {
            // Guild context - try to fetch member
            try {
                member = await interaction.guild.members.fetch(user.id);
                displayName = member.displayName;
            } catch (error) {
                // User not in guild, fall back to username
                displayName = user.displayName || user.username;
            }
        } else {
            // User app context - use user's display name or username
            displayName = user.displayName || user.username;
        }

        // Normalize the name before using it for gender detection
        const rawName = displayName.split(' ')[0];
        const normalizedName = this.normalizeText(rawName);
        
        const response = await axios.get(`https://api.genderize.io/?name=${encodeURIComponent(normalizedName)}`);
        const data = response.data;

        if (data.gender === 'male') { //male

            let minLength, maxLength;
            
            if (specialUserIds.includes(user.id)) {
                // Special users get big pp
                minLength = 50;
                maxLength = 80;
            } else if (smallUsersIds.includes(user.id)) {
                // Small users get shortest pp
                minLength = 1;
                maxLength = 2;
            } else {
                // Normal users
                minLength = 1;
                maxLength = 30;
            }

            const length = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;

            const dickSize = '8' + '='.repeat(length) + 'D';
            const responseMessage = `**${displayName}'s size:**\n${dickSize}`;
            await interaction.reply({ content: responseMessage });


        } else if (data.gender === 'female') { //female
            const responseMessage = `**${displayName}** doesn't have a pp.`;
            await interaction.reply({ content: responseMessage });


        } else { //no idea
            const responseMessage = `I couldn't determine the gender of **${displayName}**.`;
            await interaction.reply({ content: responseMessage });
        }
    },
};
