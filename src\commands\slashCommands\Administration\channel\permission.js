const { 
    <PERSON><PERSON><PERSON>ommandSubcommand<PERSON>uilder, 
    EmbedBuilder, 
    PermissionFlagsBits, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    ChannelType 
} = require('discord.js');

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('permission')
        .setDescription('Modify all or specific channel/category permissions for a role or everyone')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to modify permissions for')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('permission')
                .setDescription('The permission to modify')
                .setRequired(true)
                .addChoices(
                    { name: 'View Channel', value: 'ViewChannel' },
                    { name: 'Send Messages', value: 'SendMessages' },
                    { name: 'Send Messages in Threads', value: 'SendMessagesInThreads' },
                    { name: 'Create Public Threads', value: 'CreatePublicThreads' },
                    { name: 'Create Private Threads', value: 'CreatePrivateThreads' },
                    { name: 'Embed Links', value: 'EmbedLinks' },
                    { name: 'Attach Files', value: 'AttachFiles' },
                    { name: 'Add Reactions', value: 'AddReactions' },
                    { name: 'Use External Emojis', value: 'UseExternalEmojis' },
                    { name: 'Use External Stickers', value: 'UseExternalStickers' },
                    { name: 'Mention Everyone', value: 'MentionEveryone' },
                    { name: 'Manage Messages', value: 'ManageMessages' },
                    { name: 'Manage Threads', value: 'ManageThreads' },
                    { name: 'Read Message History', value: 'ReadMessageHistory' },
                    { name: 'Use Slash Commands', value: 'UseApplicationCommands' },
                    { name: 'Connect (Voice)', value: 'Connect' },
                    { name: 'Speak (Voice)', value: 'Speak' },
                    { name: 'Stream (Voice)', value: 'Stream' },
                    { name: 'Use Voice Activity', value: 'UseVAD' },
                    { name: 'Priority Speaker', value: 'PrioritySpeaker' },
                    { name: 'Mute Members', value: 'MuteMembers' },
                    { name: 'Deafen Members', value: 'DeafenMembers' },
                    { name: 'Move Members', value: 'MoveMembers' }
                ))
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Set permission state: Enable, Disable, or Unset (middle)')
                .setRequired(true)
                .addChoices(
                    { name: 'Enable', value: 'enable' },
                    { name: 'Disable', value: 'disable' },
                    { name: 'Unset (Middle State)', value: 'unset' }
                ))
        .addChannelOption(option =>
            option.setName('target')
                .setDescription('Target channel or category (leave empty for current channel)'))
        .addStringOption(option =>
            option.setName('scope')
                .setDescription('Apply to specific channel types or all channels')
                .addChoices(
                    { name: 'All Text Channels', value: 'ALL_TEXT' },
                    { name: 'All Voice Channels', value: 'ALL_VOICE' },
                    { name: 'All Stage Channels', value: 'ALL_STAGE' },
                    { name: 'All Forum Channels', value: 'ALL_FORUM' },
                    { name: 'All Announcement Channels', value: 'ALL_ANNOUNCEMENT' },
                    { name: 'ALL CHANNELS (literally includes everything)', value: 'ALL' }
                )),

    async execute(interaction, { permissionSessions, SESSION_TIMEOUT }) {
        // Check admin permission for permission subcommand
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            return await interaction.reply({
                content: '❌ You need Administrator permission to use the permission subcommand.',
                ephemeral: true
            });
        }

        // Check for existing permission operation session
        const existingSession = permissionSessions.get(interaction.user.id);
        if (existingSession) {
            // Calculate remaining time
            const currentTime = Date.now();
            const sessionStartTime = existingSession.startTime;
            const elapsedTime = currentTime - sessionStartTime;
            const remainingTime = SESSION_TIMEOUT - elapsedTime;
            
            if (remainingTime > 0) {
                const remainingMinutes = Math.ceil(remainingTime / (60 * 1000));
                return await interaction.reply({
                    content: `⚠️ You already have an active permission change operation. Please wait for it to complete or wait **${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}** and try again.`,
                    ephemeral: true
                });
            } else {
                // Session has expired, remove it
                permissionSessions.delete(interaction.user.id);
            }
        }

        const role = interaction.options.getRole('role');
        const permission = interaction.options.getString('permission');
        const action = interaction.options.getString('action');
        const targetChannel = interaction.options.getChannel('target');
        const scope = interaction.options.getString('scope');

        // Determine target channels
        let targetChannels = [];
        let targetDescription = '';

        if (scope) {
            // Apply to specific channel types or all channels
            switch (scope) {
                case 'ALL':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildText || 
                        channel.type === ChannelType.GuildVoice || 
                        channel.type === ChannelType.GuildStageVoice ||
                        channel.type === ChannelType.GuildForum ||
                        channel.type === ChannelType.GuildAnnouncement
                    );
                    targetDescription = `**ALL CHANNELS** (${targetChannels.size} channels)`;
                    break;
                case 'ALL_TEXT':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildText
                    );
                    targetDescription = `**All Text Channels** (${targetChannels.size} channels)`;
                    break;
                case 'ALL_VOICE':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildVoice
                    );
                    targetDescription = `**All Voice Channels** (${targetChannels.size} channels)`;
                    break;
                case 'ALL_STAGE':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildStageVoice
                    );
                    targetDescription = `**All Stage Channels** (${targetChannels.size} channels)`;
                    break;
                case 'ALL_FORUM':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildForum
                    );
                    targetDescription = `**All Forum Channels** (${targetChannels.size} channels)`;
                    break;
                case 'ALL_ANNOUNCEMENT':
                    targetChannels = interaction.guild.channels.cache.filter(channel => 
                        channel.type === ChannelType.GuildAnnouncement
                    );
                    targetDescription = `**All Announcement Channels** (${targetChannels.size} channels)`;
                    break;
            }
        } else if (targetChannel) {
            if (targetChannel.type === ChannelType.GuildCategory) {
                // Apply to all channels in the category
                targetChannels = interaction.guild.channels.cache.filter(channel => 
                    channel.parentId === targetChannel.id &&
                    (channel.type === ChannelType.GuildText || 
                     channel.type === ChannelType.GuildVoice || 
                     channel.type === ChannelType.GuildStageVoice ||
                     channel.type === ChannelType.GuildForum ||
                     channel.type === ChannelType.GuildAnnouncement)
                );
                targetDescription = `**Category: ${targetChannel.name}** (${targetChannels.size} channels)`;
            } else {
                // Apply to specific channel
                targetChannels = new Map([[targetChannel.id, targetChannel]]);
                targetDescription = `**Channel: ${targetChannel.name}**`;
            }
        } else {
            // Apply to current channel
            targetChannels = new Map([[interaction.channel.id, interaction.channel]]);
            targetDescription = `**Current Channel: ${interaction.channel.name}**`;
        }

        // Convert permission string to Discord.js permission flag
        const permissionFlag = PermissionFlagsBits[permission];
        if (!permissionFlag) {
            return await interaction.reply({
                content: '❌ Invalid permission specified.',
                ephemeral: true
            });
        }

        // Get action description for embed
        let actionDescription = '';
        switch (action) {
            case 'enable':
                actionDescription = 'ENABLE (Allow)';
                break;
            case 'disable':
                actionDescription = 'DISABLE (Deny)';
                break;
            case 'unset':
                actionDescription = 'UNSET (Middle State)';
                break;
        }

        // Create confirmation embed
        const confirmEmbed = new EmbedBuilder()
            .setTitle('🔧 Channel Permission Confirmation')
            .setDescription('Please confirm the following permission changes:')
            .addFields(
                { name: '👥 Role', value: `${role}`, inline: true },
                { name: '🔐 Permission', value: `**${permission.replace(/([A-Z])/g, ' $1').trim()}**`, inline: true },
                { name: '⚙️ Action', value: `**${actionDescription}**`, inline: true },
                { name: '🎯 Target', value: targetDescription, inline: false }
            )
            .setColor(
                action === 'enable' ? '#2ecc71' : 
                action === 'disable' ? '#e74c3c' : 
                '#95a5a6' // unset
            )
            .setTimestamp()
            .setFooter({ text: 'Click ✅ to confirm or ❌ to cancel' });

        // Create confirmation buttons
        const confirmButton = new ButtonBuilder()
            .setCustomId(`confirm_channelperm_${interaction.id}`)
            .setLabel('✅ Confirm')
            .setStyle(ButtonStyle.Success);

        const cancelButton = new ButtonBuilder()
            .setCustomId(`cancel_channelperm_${interaction.id}`)
            .setLabel('❌ Cancel')
            .setStyle(ButtonStyle.Danger);

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        // Store the permission data for the button handler
        if (!interaction.client.pendingPermissions) {
            interaction.client.pendingPermissions = new Map();
        }

        interaction.client.pendingPermissions.set(interaction.id, {
            role,
            permission,
            permissionFlag,
            action,
            targetChannels,
            targetDescription,
            userId: interaction.user.id
        });

        // Send confirmation message
        await interaction.reply({
            embeds: [confirmEmbed],
            components: [row],
            ephemeral: true
        });

        // Set timeout to clean up stored data
        setTimeout(() => {
            if (interaction.client.pendingPermissions) {
                interaction.client.pendingPermissions.delete(interaction.id);
            }
        }, 300000); // 5 minutes
    },

    async handleButtonInteraction(interaction, { permissionSessions, SESSION_TIMEOUT }) {
        // Handle permission buttons
        if (!interaction.customId.includes('channelperm_')) return;

        const [action, , interactionId] = interaction.customId.split('_');
        
        if (!interaction.client.pendingPermissions || !interaction.client.pendingPermissions.has(interactionId)) {
            return await interaction.reply({
                content: '❌ This permission request has expired. Please run the command again.',
                ephemeral: true
            });
        }

        const permissionData = interaction.client.pendingPermissions.get(interactionId);

        // Check if the user who clicked is the same as who initiated the command
        if (permissionData.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ Only the user who initiated this command can confirm it.',
                ephemeral: true
            });
        }

        if (action === 'cancel') {
            interaction.client.pendingPermissions.delete(interactionId);
            
            const cancelEmbed = new EmbedBuilder()
                .setTitle('❌ Permission Change Cancelled')
                .setDescription('The permission change has been cancelled.')
                .setColor('#95a5a6')
                .setTimestamp();

            return await interaction.update({
                embeds: [cancelEmbed],
                components: []
            });
        }

        if (action === 'confirm') {
            await interaction.deferUpdate();

            try {
                const { role, permission, permissionFlag, action: permAction, targetChannels, targetDescription } = permissionData;
                
                // Create session to prevent concurrent operations
                permissionSessions.set(interaction.user.id, {
                    startTime: Date.now(),
                    operation: 'permission_change',
                    targetChannels: targetChannels.size
                });

                let successCount = 0;
                let failCount = 0;
                const errors = [];
                const totalChannels = targetChannels.size;

                // Create progress embed for bulk operations (only if more than 3 channels)
                if (totalChannels > 3) {
                    const progressEmbed = new EmbedBuilder()
                        .setColor('#0099ff')
                        .setTitle('🔄 Applying Permission Changes')
                        .setDescription('Please wait while I update channel permissions...')
                        .addFields([
                            { name: '📊 Progress', value: `0/${totalChannels} channels processed`, inline: true },
                            { name: '✅ Success', value: '0', inline: true },
                            { name: '❌ Failed', value: '0', inline: true }
                        ])
                        .setTimestamp();

                    await interaction.editReply({
                        embeds: [progressEmbed],
                        components: []
                    });
                }

                // Apply permission changes to all target channels
                let channelCount = 0;
                for (const [channelId, channel] of targetChannels) {
                    try {
                        channelCount++;

                        // Update progress for bulk operations (only if more than 3 channels)
                        if (totalChannels > 3) {
                            const progressEmbed = new EmbedBuilder()
                                .setColor('#0099ff')
                                .setTitle('🔄 Applying Permission Changes')
                                .setDescription('Please wait while I update channel permissions...')
                                .addFields([
                                    { name: '📊 Progress', value: `${channelCount}/${totalChannels} channels processed`, inline: true },
                                    { name: '✅ Success', value: `${successCount}`, inline: true },
                                    { name: '❌ Failed', value: `${failCount}`, inline: true }
                                ])
                                .setTimestamp();

                            await interaction.editReply({ embeds: [progressEmbed] });
                        }

                        // Handle permission overwrites based on action
                        const permissionOverwrites = {};
                        
                        if (permAction === 'enable') {
                            permissionOverwrites[permissionFlag] = true;
                        } else if (permAction === 'disable') {
                            permissionOverwrites[permissionFlag] = false;
                        } else if (permAction === 'unset') {
                            // For unset (middle state), we set it to null which creates a neutral override
                            permissionOverwrites[permissionFlag] = null;
                        }

                        await channel.permissionOverwrites.edit(role, permissionOverwrites, {
                            reason: `Bulk permission change by ${interaction.user.tag}`
                        });

                        successCount++;

                        // Small delay to avoid rate limits for bulk operations
                        if (totalChannels > 10) {
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                    } catch (error) {
                        failCount++;
                        errors.push(`${channel.name}: ${error.message}`);
                        console.error(`Error updating permissions for channel ${channel.name}:`, error);
                    }
                }

                // Clear the session
                permissionSessions.delete(interaction.user.id);

                // Create result embed
                const resultEmbed = new EmbedBuilder()
                    .setTitle('🔧 Permission Changes Applied')
                    .addFields(
                        { name: '👥 Role', value: `${role}`, inline: true },
                        { name: '🔐 Permission', value: `**${permission.replace(/([A-Z])/g, ' $1').trim()}**`, inline: true },
                        { name: '🎯 Target', value: targetDescription, inline: false },
                        { name: '✅ Successful', value: `${successCount}`, inline: true },
                        { name: '❌ Failed', value: `${failCount}`, inline: true },
                        { name: '📊 Total', value: `${totalChannels}`, inline: true }
                    )
                    .setColor(failCount === 0 ? '#2ecc71' : (successCount === 0 ? '#e74c3c' : '#f39c12'))
                    .setTimestamp();

                if (errors.length > 0 && errors.length <= 5) {
                    resultEmbed.addFields({
                        name: '⚠️ Errors',
                        value: errors.slice(0, 5).map(error => `• ${error}`).join('\n'),
                        inline: false
                    });
                } else if (errors.length > 5) {
                    resultEmbed.addFields({
                        name: '⚠️ Errors',
                        value: `${errors.slice(0, 3).map(error => `• ${error}`).join('\n')}\n... and ${errors.length - 3} more errors`,
                        inline: false
                    });
                }

                await interaction.editReply({
                    embeds: [resultEmbed],
                    components: []
                });

                // Clean up stored data
                interaction.client.pendingPermissions.delete(interactionId);

            } catch (error) {
                console.error('Error applying permission changes:', error);
                
                // Clear the session on error
                permissionSessions.delete(interaction.user.id);
                
                const errorEmbed = new EmbedBuilder()
                    .setTitle('❌ Error Applying Changes')
                    .setDescription('An error occurred while applying the permission changes. Please check the bot\'s permissions and try again.')
                    .setColor('#e74c3c')
                    .setTimestamp();

                await interaction.editReply({
                    embeds: [errorEmbed],
                    components: []
                });

                // Clean up stored data
                interaction.client.pendingPermissions.delete(interactionId);
            }
        }
    }
};
