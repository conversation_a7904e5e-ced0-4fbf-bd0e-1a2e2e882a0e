const { 
    <PERSON><PERSON><PERSON>ommandSubcommand<PERSON><PERSON>er, 
    EmbedBuilder, 
    PermissionFlagsBits, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    ChannelType 
} = require('discord.js');

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('info')
        .setDescription('Get detailed information about a channel')
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel to get information about (leave empty for current channel)')),

    async execute(interaction, sharedData) {
        await this.handleInfoSubcommand(interaction, false);
    },

    async handleButtonInteraction(interaction) {
        // Handle channel info buttons
        if (interaction.customId.startsWith('channelinfo_')) {
            const [, viewType, channelId, originalId] = interaction.customId.split('_');
            
            // Verify admin permission
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Only administrators can view detailed channel information.',
                    ephemeral: true
                });
            }
            
            // Get the channel
            const channel = interaction.guild.channels.cache.get(channelId);
            if (!channel) {
                return await interaction.reply({
                    content: '❌ Channel not found.',
                    ephemeral: true
                });
            }
            
            // Check if data exists
            const dataKey = `${channelId}_${originalId}`;
            if (interaction.client.channelInfoData && interaction.client.channelInfoData.has(dataKey)) {
                const data = interaction.client.channelInfoData.get(dataKey);
                
                // Verify the user
                if (data.userId !== interaction.user.id) {
                    return await interaction.reply({
                        content: '❌ You can only interact with your own channel info requests.',
                        ephemeral: true
                    });
                }
            }
            
            // Update the view
            interaction.channel = channel; // Set the channel for the handler
            await this.handleInfoSubcommand(interaction, viewType === 'detailed');
            return;
        }
    },

    async handleInfoSubcommand(interaction, showDetailed = false) {
        const targetChannel = interaction.options?.getChannel('channel') || interaction.message?.mentions?.channels?.first() || interaction.channel;
        const isAdmin = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
        
        // Get channel type name and emoji
        const getChannelTypeInfo = (type) => {
            switch (type) {
                case ChannelType.GuildText: return { name: 'Text Channel', emoji: '💬' };
                case ChannelType.GuildVoice: return { name: 'Voice Channel', emoji: '🔊' };
                case ChannelType.GuildCategory: return { name: 'Category', emoji: '📁' };
                case ChannelType.GuildAnnouncement: return { name: 'Announcement Channel', emoji: '📢' };
                case ChannelType.GuildStageVoice: return { name: 'Stage Channel', emoji: '🎭' };
                case ChannelType.GuildForum: return { name: 'Forum Channel', emoji: '💭' };
                case ChannelType.PublicThread: return { name: 'Public Thread', emoji: '🧵' };
                case ChannelType.PrivateThread: return { name: 'Private Thread', emoji: '🔒' };
                case ChannelType.AnnouncementThread: return { name: 'Announcement Thread', emoji: '📣' };
                default: return { name: 'Unknown', emoji: '❓' };
            }
        };

        // Format timestamp
        const formatTimestamp = (timestamp) => {
            return timestamp ? `<t:${Math.floor(timestamp / 1000)}:F>\n<t:${Math.floor(timestamp / 1000)}:R>` : 'N/A';
        };

        const channelTypeInfo = getChannelTypeInfo(targetChannel.type);
        
        // Choose embed color based on channel type
        const getEmbedColor = () => {
            switch (targetChannel.type) {
                case ChannelType.GuildText: return '#5865F2';
                case ChannelType.GuildVoice: return '#3BA55C';
                case ChannelType.GuildStageVoice: return '#EB459E';
                case ChannelType.GuildForum: return '#F47B67';
                case ChannelType.GuildAnnouncement: return '#F9A72B';
                case ChannelType.GuildCategory: return '#99AAB5';
                default: return '#5865F2';
            }
        };

        // Basic channel info embed
        const infoEmbed = new EmbedBuilder()
            .setAuthor({ 
                name: `${channelTypeInfo.emoji} ${channelTypeInfo.name}`,
                iconURL: interaction.guild.iconURL({ dynamic: true })
            })
            .setTitle(`${targetChannel.name}`)
            .setColor(getEmbedColor())
            .setDescription(`\`\`\`${targetChannel.id}\`\`\``)
            .addFields(
                { 
                    name: '⏰ Created', 
                    value: `${formatTimestamp(targetChannel.createdTimestamp)}`, 
                    inline: true 
                }
            )
            .setTimestamp()
            .setFooter({ 
                text: `Requested by ${interaction.user.tag}${showDetailed ? ' • Detailed View' : ' • Basic View'}`, 
                iconURL: interaction.user.displayAvatarURL() 
            });

        // Add category info if applicable
        if (targetChannel.parent) {
            infoEmbed.addFields({ 
                name: '📂 Parent Category', 
                value: `${targetChannel.parent.name}\n\`${targetChannel.parentId}\``, 
                inline: true 
            });
        }

        // Add position info
        if (targetChannel.position !== undefined) {
            infoEmbed.addFields({ 
                name: '📊 Position', 
                value: `\`#${targetChannel.position + 1}\``, 
                inline: true 
            });
        }

        // Channel-specific information with better formatting
        if (targetChannel.type === ChannelType.GuildText || targetChannel.type === ChannelType.GuildAnnouncement) {
            // Add a separator field
            infoEmbed.addFields({ name: '\u200B', value: '**━━━ Channel Settings ━━━**', inline: false });
            
            // Always show basic info
            if (targetChannel.topic) {
                infoEmbed.addFields({ 
                    name: '📋 Channel Topic', 
                    value: `> ${targetChannel.topic.substring(0, 1024)}`, 
                    inline: false 
                });
            }
            
            // Text channel specific info
            const nsfwStatus = targetChannel.nsfw ? '✅ Enabled' : '❌ Disabled';
            const slowmodeStatus = targetChannel.rateLimitPerUser 
                ? `⏱️ ${targetChannel.rateLimitPerUser}s` 
                : '🚀 None';
            
            infoEmbed.addFields(
                { 
                    name: '🔞 NSFW', 
                    value: `\`\`\`${nsfwStatus}\`\`\``, 
                    inline: true 
                },
                { 
                    name: '⌛ Slowmode', 
                    value: `\`\`\`${slowmodeStatus}\`\`\``, 
                    inline: true 
                }
            );

            // Thread info - show only in detailed view
            if (showDetailed && targetChannel.threads) {
                const activeThreads = targetChannel.threads.cache.filter(thread => !thread.archived);
                const archivedThreads = targetChannel.threads.cache.filter(thread => thread.archived);
                
                const threadInfo = [
                    `🟢 **Active:** ${activeThreads.size}`,
                    `🔒 **Archived:** ${archivedThreads.size}`,
                    `📊 **Total:** ${activeThreads.size + archivedThreads.size}`
                ].join('\n');
                
                infoEmbed.addFields({
                    name: '🧵 Thread Statistics',
                    value: threadInfo,
                    inline: true
                });
            }
        } else if (targetChannel.type === ChannelType.GuildVoice || targetChannel.type === ChannelType.GuildStageVoice) {
            // Add a separator field
            infoEmbed.addFields({ name: '\u200B', value: '**━━━ Voice Settings ━━━**', inline: false });
            
            // Voice channel specific info
            const userLimitDisplay = targetChannel.userLimit 
                ? `👥 ${targetChannel.userLimit} users` 
                : '♾️ Unlimited';
            
            infoEmbed.addFields(
                { 
                    name: '🎯 User Limit', 
                    value: `\`\`\`${userLimitDisplay}\`\`\``, 
                    inline: true 
                },
                { 
                    name: '🎵 Bitrate', 
                    value: `\`\`\`${targetChannel.bitrate / 1000} kbps\`\`\``, 
                    inline: true 
                },
                { 
                    name: '🌍 Region', 
                    value: `\`\`\`${targetChannel.rtcRegion || 'Auto'}\`\`\``, 
                    inline: true 
                }
            );
            
            if (targetChannel.type === ChannelType.GuildVoice) {
                infoEmbed.addFields({ 
                    name: '📹 Video Quality', 
                    value: `\`\`\`${targetChannel.videoQualityMode === 1 ? 'Auto' : '720p'}\`\`\``, 
                    inline: true 
                });
            }

            // Current members in voice channel
            const members = targetChannel.members;
            if (members.size > 0) {
                infoEmbed.addFields({ name: '\u200B', value: '**━━━ Active Session ━━━**', inline: false });
                
                const memberList = members.map(m => `🔹 **${m.user.tag}**`).slice(0, 10).join('\n');
                const moreMembers = members.size > 10 ? `\n*... and ${members.size - 10} more*` : '';
                
                infoEmbed.addFields({
                    name: `🎤 Connected Users (${members.size}/${targetChannel.userLimit || '∞'})`,
                    value: `${memberList}${moreMembers}`,
                    inline: false
                });
            } else {
                infoEmbed.addFields({
                    name: '🎤 Connected Users',
                    value: '```No users connected```',
                    inline: false
                });
            }
        } else if (targetChannel.type === ChannelType.GuildForum) {
            // Forum channel handling
            infoEmbed.addFields({ name: '\u200B', value: '**━━━ Forum Settings ━━━**', inline: false });
            
            const nsfwStatus = targetChannel.nsfw ? '✅ Enabled' : '❌ Disabled';
            const slowmodeStatus = targetChannel.rateLimitPerUser ? `⏱️ ${targetChannel.rateLimitPerUser}s` : '🚀 None';
            
            infoEmbed.addFields(
                { name: '🔞 NSFW', value: `\`\`\`${nsfwStatus}\`\`\``, inline: true },
                { name: '⌛ Slowmode', value: `\`\`\`${slowmodeStatus}\`\`\``, inline: true }
            );
        } else if (targetChannel.type === ChannelType.GuildCategory) {
            // Category handling
            infoEmbed.addFields({ name: '\u200B', value: '**━━━ Category Overview ━━━**', inline: false });
            
            const channels = interaction.guild.channels.cache.filter(ch => ch.parentId === targetChannel.id);
            infoEmbed.addFields({
                name: `📊 Total Channels`,
                value: `\`\`\`${channels.size} channels\`\`\``,
                inline: false
            });
        }

        // Add URL field
        infoEmbed.addFields({ 
            name: '🔗 Channel Link', 
            value: `[Jump to Channel](https://discord.com/channels/${interaction.guild.id}/${targetChannel.id})`, 
            inline: false 
        });

        // Create components array
        const components = [];
        
        // Add detailed view button for admins
        if (isAdmin && !showDetailed) {
            const detailButton = new ButtonBuilder()
                .setCustomId(`channelinfo_detailed_${targetChannel.id}_${interaction.id}`)
                .setLabel('View Detailed Info')
                .setEmoji('🔍')
                .setStyle(ButtonStyle.Primary);
            
            const row = new ActionRowBuilder().addComponents(detailButton);
            components.push(row);
            
            // Store channel info for button handler
            if (!interaction.client.channelInfoData) {
                interaction.client.channelInfoData = new Map();
            }
            
            interaction.client.channelInfoData.set(`${targetChannel.id}_${interaction.id}`, {
                channelId: targetChannel.id,
                userId: interaction.user.id,
                timestamp: Date.now()
            });
            
            // Clean up after 5 minutes
            setTimeout(() => {
                if (interaction.client.channelInfoData) {
                    interaction.client.channelInfoData.delete(`${targetChannel.id}_${interaction.id}`);
                }
            }, 300000);
        } else if (isAdmin && showDetailed) {
            const basicButton = new ButtonBuilder()
                .setCustomId(`channelinfo_basic_${targetChannel.id}_${interaction.id}`)
                .setLabel('View Basic Info')
                .setEmoji('📄')
                .setStyle(ButtonStyle.Secondary);
            
            const row = new ActionRowBuilder().addComponents(basicButton);
            components.push(row);
        }

        // Send the embed
        const replyOptions = { embeds: [infoEmbed] };
        if (components.length > 0) {
            replyOptions.components = components;
        }
        
        // Check if this is a button interaction that needs updating
        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    }
};
