const { createCanvas, loadImage, GlobalFonts } = require('@napi-rs/canvas');
const path = require('path');

// Define theme-wide constants - Enhanced with vibrant colors and gradients
const COLORS = {
    DEFAULT: '#164ee9',    // Bot theme blue
    SUCCESS: '#32CD32',    // Lime Green
    ERROR: '#DC143C',      // Crimson
    WARNING: '#FFD700',    // Gold
    // INFO: '#696969',        // Dark Grey',       
    // MISC: '#000000ff',
    // EVENTS: `#00ffff`,     // Purple
    // GAMES: '#800080',
    // ADMIN: '#FFFF00',      // Red
    // MODERATION: '#F04747', // Red
    // GENERAL: '#164ee9',    // Bot theme blue
    // UTILITY: '#7289DA',    // Discord Blurple
    // FUN: '#57F287',        // Green
    DEVELOPER: '#164ee9',  // Bot theme blue
    SPOTIFY: '#1DB954',    // Spotify Green
    //INFORMATION: '#164ee9',// Bot theme blue
    // Enhanced background gradient colors - Deeper blue theme
    BG_START: '#081029',   // Deep Dark Blue
    BG_MID: '#1e56f0',     // Vibrant Bot theme blue
    BG_END: '#0a1a3a',     // Dark Blue
    // Enhanced glass panel colors with more depth
    GLASS_PRIMARY: 'rgba(30, 86, 240, 0.65)',    // Enhanced primary glass
    GLASS_BORDER: 'rgba(65, 120, 255, 0.85)',    // Brighter border
    GLASS_SECONDARY: 'rgba(22, 78, 233, 0.45)',  // Secondary glass
    GLASS_CONTENT: 'rgba(22, 78, 233, 0.35)',    // Content glass
    // New accent colors for highlights
    ACCENT_GLOW: 'rgba(65, 120, 255, 0.9)',      // Bright blue glow
    ACCENT_HIGHLIGHT: '#41b0ff',                 // Light blue highlight
    ACCENT_SECONDARY: '#ff9142',                 // Orange accent for contrast
};

/**
 * Manages creation of canvas-based help images
 */
class HelpCanvas {
    constructor() {
        this.initFonts();
    }

    initFonts() {
        try {
            // Try to register fonts, fallback to system fonts if not available
            console.log('Initializing fonts for help canvas...');
        } catch (error) {
            console.error('Failed to load fonts:', error);
        }
    }

    getFonts() {
        return {
            main: 'Arial',
            bold: 'Arial'
        };
    }

    getCategoryColor(category) {
        const catLower = category?.toLowerCase() || 'default';
        const colorMap = {
            misc: COLORS.MISC,
            admin: COLORS.ADMIN,
            games: COLORS.GAMES,
            events: COLORS.EVENTS,
            // Red for admin
            utility: COLORS.UTILITY,
            general: COLORS.GENERAL,
            developer: COLORS.DEVELOPER,
            owner: COLORS.DEVELOPER,
            fun: COLORS.FUN,        // Green for fun
            spotify: COLORS.SPOTIFY,
            information: COLORS.INFO,
            moderation: COLORS.MODERATION, // Red for moderation
        };
        return colorMap[catLower] || COLORS.DEFAULT;
    }

    formatCategoryName(category) {
        if (!category) return 'Unknown';
        return category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
    }

    truncateText(ctx, text = '', maxWidth) {
        if (!text) return '';
        if (ctx.measureText(text).width <= maxWidth) return text;
        let truncated = text;
        while (ctx.measureText(truncated + '...').width > maxWidth && truncated.length > 0) {
            truncated = truncated.slice(0, -1);
        }
        return truncated.length === 0 ? '...' : truncated + '...';
    }

    drawGlassPanel(ctx, x, y, width, height, radius, fillColor, strokeColor = null, strokeWidth = 1) {
        ctx.save();
        
        // Create glass panel shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 15;
        ctx.shadowOffsetY = 5;
        
        // Main panel background
        ctx.fillStyle = fillColor;
        ctx.beginPath();
        ctx.roundRect(x, y, width, height, radius);
        ctx.fill();
        
        // Reset shadow for inner elements
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetY = 0;
        
        // Enhanced top highlight for glass effect
        const topGradient = ctx.createLinearGradient(x, y, x, y + height * 0.15);
        topGradient.addColorStop(0, 'rgba(255, 255, 255, 0.25)');
        topGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        ctx.fillStyle = topGradient;
        ctx.beginPath();
        ctx.roundRect(x, y, width, height * 0.15, radius);
        ctx.fill();
        
        // Add subtle bottom shadow for depth
        const bottomGradient = ctx.createLinearGradient(x, y + height * 0.85, x, y + height);
        bottomGradient.addColorStop(0, 'rgba(0, 0, 0, 0)');
        bottomGradient.addColorStop(1, 'rgba(0, 0, 0, 0.15)');
        ctx.fillStyle = bottomGradient;
        ctx.beginPath();
        ctx.roundRect(x, y + height * 0.85, width, height * 0.15, radius);
        ctx.fill();
        
        // Enhanced border with glow effect
        if (strokeColor) {
            ctx.strokeStyle = strokeColor;
            ctx.lineWidth = strokeWidth;
            
            // Add subtle glow to borders
            ctx.shadowColor = COLORS.ACCENT_GLOW;
            ctx.shadowBlur = 5;
            
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            ctx.stroke();
            
            // Add highlight to top-left corner
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(x + radius, y + radius, radius * 0.8, Math.PI, 1.5 * Math.PI);
            ctx.stroke();
        }
        
        ctx.restore();
    }

    createBackgroundEffects(ctx, width, height) {
        // Enhanced gradient background with more depth
        const bgGradient = ctx.createLinearGradient(0, 0, width, height);
        bgGradient.addColorStop(0, COLORS.BG_START);
        bgGradient.addColorStop(0.4, COLORS.BG_MID);
        bgGradient.addColorStop(1, COLORS.BG_END);
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, width, height);
        
        // Add subtle grid pattern
        ctx.strokeStyle = 'rgba(65, 120, 255, 0.1)';
        ctx.lineWidth = 1;
        const gridSize = 40;
        for (let x = 0; x < width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        for (let y = 0; y < height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }

        // Enhanced star particles with varying sizes
        for (let i = 0; i < 180; i++) {
            const x = Math.random() * width;
            const y = Math.random() * height;
            const radius = Math.random() * 2;
            const alpha = Math.random() * 0.7;
            ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
        }

        // Add larger glowing orbs with animation-like effect
        for (let i = 0; i < 25; i++) {
            const x = Math.random() * width;
            const y = Math.random() * height;
            const radius = 2 + Math.random() * 4;
            
            // Create glow effect
            ctx.fillStyle = 'rgba(30, 86, 240, 0.3)';
            ctx.shadowColor = COLORS.ACCENT_GLOW;
            ctx.shadowBlur = 20;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Add inner highlight
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.shadowBlur = 5;
            ctx.beginPath();
            ctx.arc(x, y, radius * 0.4, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;
        }
        
        // Add subtle diagonal light beams
        ctx.globalAlpha = 0.05;
        for (let i = 0; i < 5; i++) {
            const startX = Math.random() * width;
            const gradient = ctx.createLinearGradient(startX, 0, startX + 300, height);
            gradient.addColorStop(0, 'rgba(65, 120, 255, 0)');
            gradient.addColorStop(0.5, 'rgba(65, 120, 255, 0.3)');
            gradient.addColorStop(1, 'rgba(65, 120, 255, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.moveTo(startX, 0);
            ctx.lineTo(startX + 300, height);
            ctx.lineTo(startX + 400, height);
            ctx.lineTo(startX + 100, 0);
            ctx.closePath();
            ctx.fill();
        }
        ctx.globalAlpha = 1.0;
    }

    async generateHomePage(client, prefix, categories) {
        const width = 1200;
        const height = 600;
        const canvas = createCanvas(width, height);
        const ctx = canvas.getContext('2d');
        const fonts = this.getFonts();

        // Create enhanced background with particles and effects
        this.createBackgroundEffects(ctx, width, height);
        
        // Add decorative hexagon pattern in background
        ctx.strokeStyle = 'rgba(65, 120, 255, 0.15)';
        ctx.lineWidth = 1;
        const hexSize = 60;
        const hexHeight = hexSize * Math.sqrt(3);
        for (let row = -1; row < height/hexHeight + 1; row++) {
            for (let col = -1; col < width/hexSize + 1; col++) {
                const x = col * hexSize * 1.5;
                const y = row * hexHeight + (col % 2 === 0 ? 0 : hexHeight/2);
                ctx.beginPath();
                for (let i = 0; i < 6; i++) {
                    const angle = 2 * Math.PI / 6 * i;
                    const hx = x + hexSize * Math.cos(angle);
                    const hy = y + hexSize * Math.sin(angle);
                    if (i === 0) ctx.moveTo(hx, hy);
                    else ctx.lineTo(hx, hy);
                }
                ctx.closePath();
                ctx.stroke();
            }
        }

        // Add decorative accent circles with glow
        ctx.fillStyle = 'rgba(255,255,255,0.13)';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 15;
        ctx.beginPath(); ctx.arc(120, 120, 40, 0, Math.PI * 2); ctx.fill();
        ctx.beginPath(); ctx.arc(width-120, 180, 30, 0, Math.PI * 2); ctx.fill();
        ctx.shadowBlur = 0;

        // Enhanced bot avatar with glowing border - positioned for landscape layout
        let avatarImg;
        try {
            const avatarURL = client.user.displayAvatarURL({ format: 'png', size: 128 });
            avatarImg = await loadImage(avatarURL);
        } catch (error) {}

        if (avatarImg) {
            // Draw glowing circle behind avatar - positioned left side
            ctx.save();
            ctx.shadowColor = COLORS.ACCENT_GLOW;
            ctx.shadowBlur = 20;
            ctx.fillStyle = COLORS.GLASS_PRIMARY;
            ctx.beginPath();
            ctx.arc(150, 90, 42, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;

            // Draw avatar
            ctx.beginPath();
            ctx.arc(150, 90, 38, 0, Math.PI * 2);
            ctx.clip();
            ctx.drawImage(avatarImg, 112, 52, 76, 76);
            ctx.restore();

            // Add highlight ring
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(150, 90, 38, 0, Math.PI * 2);
            ctx.stroke();
        } else {
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.shadowColor = COLORS.ACCENT_GLOW;
            ctx.shadowBlur = 15;
            ctx.beginPath();
            ctx.arc(150, 90, 38, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;
        }

        // Enhanced main title with stronger glow - positioned for landscape layout
        ctx.font = `bold 52px Arial`;
        ctx.fillStyle = '#fff';
        ctx.textAlign = 'left';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 25;
        ctx.fillText('RankBreaker', 220, 80);

        // Add subtitle with subtle animation-like effect
        ctx.shadowBlur = 10;
        ctx.font = '26px Arial';
        ctx.fillStyle = 'rgba(255,255,255,0.95)';
        ctx.fillText('All commands at your fingertips!', 220, 115);
        ctx.shadowBlur = 0;

        // Enhanced info panel with stronger glass effect - landscape layout
        this.drawGlassPanel(ctx, 50, 160, width-100, 60, 18, COLORS.GLASS_PRIMARY, COLORS.GLASS_BORDER, 2);
        ctx.font = '18px Arial';
        ctx.fillStyle = '#fff';
        ctx.textAlign = 'center';
        ctx.fillText('Use the dropdown below to browse by category. For details, use "/help [command]".', width/2, 195);

        // Enhanced category section with stronger visual presence - landscape layout
        this.drawGlassPanel(ctx, 50, 240, width-100, 280, 18, COLORS.GLASS_CONTENT, COLORS.GLASS_BORDER, 2);

        // Add decorative header for categories
        const catHeaderGradient = ctx.createLinearGradient(50, 240, 50, 280);
        catHeaderGradient.addColorStop(0, COLORS.GLASS_PRIMARY);
        catHeaderGradient.addColorStop(1, 'rgba(30, 86, 240, 0.1)');
        ctx.fillStyle = catHeaderGradient;
        ctx.beginPath();
        ctx.roundRect(50, 240, width-100, 50, [18, 18, 0, 0]);
        ctx.fill();

        ctx.font = 'bold 28px Arial';
        ctx.fillStyle = '#fff';
        ctx.textAlign = 'left';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 10;
        ctx.fillText('Categories', 80, 275);
        ctx.shadowBlur = 0;

        // Render categories in an enhanced modern grid with hover-like effects - landscape layout
        const visibleCategories = [...categories.keys()]
            .filter(category => category.toLowerCase() !== 'developer' && category.toLowerCase() !== 'owner')
            .sort();
        const columns = 4; // More columns for landscape layout
        const itemWidth = (width - 140) / columns;
        const itemHeight = 55;
        const startY = 310;

        for (let i = 0; i < visibleCategories.length; i++) {
            const category = visibleCategories[i];
            const commandCount = categories.get(category)?.length || 0;
            const col = i % columns;
            const row = Math.floor(i / columns);
            const x = 80 + (col * itemWidth);
            const y = startY + (row * itemHeight);
            if (y + itemHeight > 500) continue;
            
            // Enhanced category panel with accent color and glow
            const categoryColor = this.getCategoryColor(category);
            
            // Create subtle glow effect for category panel - adjusted for landscape
            ctx.shadowColor = categoryColor;
            ctx.shadowBlur = 8;
            this.drawGlassPanel(ctx, x, y, itemWidth-20, 45, 10, 'rgba(22,78,233,0.25)', categoryColor, 2);
            ctx.shadowBlur = 0;

            // Add category icon/indicator - smaller for landscape
            ctx.fillStyle = categoryColor;
            ctx.beginPath();
            ctx.roundRect(x+8, y+12, 4, 18, 2);
            ctx.fill();

            // Category name with enhanced styling - smaller for landscape
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#fff';
            ctx.textAlign = 'left';
            ctx.fillText(this.formatCategoryName(category), x+18, y+25);

            // Command count with subtle styling - smaller for landscape
            ctx.font = '12px Arial';
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.fillText(`${commandCount} command${commandCount!==1?'s':''}`, x+18, y+38);
        }

        // Enhanced footer with stronger glass effect - landscape layout
        this.drawGlassPanel(ctx, 50, height-60, width-100, 45, 16, COLORS.GLASS_SECONDARY, COLORS.GLASS_BORDER, 2);
        ctx.fillStyle = 'rgba(255,255,255,0.9)';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        const serverCount = client.guilds.cache.size;
        ctx.fillText(`Serving ${serverCount} server${serverCount!==1?'s':''} | Use dropdown below to explore commands`, width/2, height-30);

        return canvas.toBuffer('image/png');
    }

    async generateCategoryPage(categoryName, allCommandsInThisCategory, prefix, client, page = 1, itemsPerPage = 24) {
        const width = 1200;
        const height = 600;
        const canvas = createCanvas(width, height);
        const ctx = canvas.getContext('2d');
        const fonts = this.getFonts();

        this.createBackgroundEffects(ctx, width, height);

        const categoryColor = this.getCategoryColor(categoryName);
        
        // Enhanced header with glow effect - landscape layout
        this.drawGlassPanel(ctx, 40, 30, width - 80, 70, 16, COLORS.GLASS_PRIMARY, COLORS.GLASS_BORDER, 2);

        // Add decorative accent line - landscape
        ctx.strokeStyle = categoryColor;
        ctx.lineWidth = 3;
        ctx.shadowColor = categoryColor;
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.moveTo(60, 100);
        ctx.lineTo(width - 60, 100);
        ctx.stroke();
        ctx.shadowBlur = 0;

        // Enhanced title with stronger glow - landscape
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 15;
        ctx.font = `bold 40px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        const formattedCategoryName = this.formatCategoryName(categoryName);
        ctx.fillText(`${formattedCategoryName.toUpperCase()} COMMANDS`, width / 2, 65);
        ctx.shadowBlur = 0;

        // Command count with enhanced styling - landscape
        ctx.font = `16px Arial`;
        ctx.fillStyle = categoryColor;
        ctx.textAlign = 'right';
        ctx.fillText(`${allCommandsInThisCategory.length} command${allCommandsInThisCategory.length !== 1 ? 's' : ''}`, width - 60, 65);

        // Enhanced info panel with category color - landscape
        this.drawGlassPanel(ctx, 40, 120, width - 80, 40, 16, categoryColor, 'rgba(255, 255, 255, 0.4)', 2);

        ctx.font = `16px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 3;
        ctx.fillText(`Use "/help [command]" for detailed information`, width / 2, 145);
        ctx.shadowBlur = 0;

        // Enhanced content panel with subtle pattern - landscape
        this.drawGlassPanel(ctx, 40, 180, width - 80, height - 240, 16, COLORS.GLASS_CONTENT, COLORS.GLASS_BORDER, 2);
        
        // Add subtle grid pattern inside content panel - landscape
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.lineWidth = 1;
        for (let x = 40; x < width - 40; x += 60) {
            ctx.beginPath();
            ctx.moveTo(x, 180);
            ctx.lineTo(x, height - 60);
            ctx.stroke();
        }

        allCommandsInThisCategory.sort((a, b) => a.name.localeCompare(b.name));

        const totalPages = Math.ceil(allCommandsInThisCategory.length / itemsPerPage);
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));
        const startIndex = (currentPage - 1) * itemsPerPage;
        const commandsToDisplay = allCommandsInThisCategory.slice(startIndex, startIndex + itemsPerPage);

        const columns = 4; // More columns for landscape
        const itemWidth = (width - 120) / columns;
        const itemHeight = 35; // Slightly smaller height
        const startXOffset = 60;
        const startYOffset = 200;

        if (commandsToDisplay.length === 0 && currentPage === 1) {
            // Enhanced empty state
            ctx.font = `bold 22px Arial`;
            ctx.textAlign = 'center';
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 5;
            ctx.fillText('No commands in this category.', width / 2, startYOffset + 100);
            ctx.shadowBlur = 0;
            
            ctx.font = `18px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.fillText('Try selecting a different category', width / 2, startYOffset + 130);
        } else {
            // Add header row with subtle styling
            // ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            // ctx.fillRect(startXOffset, startYOffset - 25, width - 90, 20);
            // ctx.font = `bold 14px Arial`;
            // ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            // ctx.textAlign = 'left';
            // ctx.fillText('COMMAND', startXOffset + 16, startYOffset - 10);
            // ctx.fillText('DESCRIPTION', startXOffset + 16 + itemWidth, startYOffset - 10);
            
            for (let i = 0; i < commandsToDisplay.length; i++) {
                const command = commandsToDisplay[i];
                const col = i % columns;
                const row = Math.floor(i / columns);
                const x = startXOffset + (col * itemWidth);
                const y = startYOffset + (row * itemHeight);

                if (y + itemHeight > (200 + height - 280 - 15)) break;

                // Enhanced command item with hover-like effect
                if (i % 2 === 0) {
                    // Create subtle gradient for even rows
                    const rowGradient = ctx.createLinearGradient(x, y, x + itemWidth - 15, y);
                    rowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.08)');
                    rowGradient.addColorStop(1, 'rgba(255, 255, 255, 0.02)');
                    ctx.fillStyle = rowGradient;
                    ctx.beginPath();
                    ctx.roundRect(x, y, itemWidth - 15, 35, 8);
                    ctx.fill();
                } else {
                    // Subtle background for odd rows
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                    ctx.beginPath();
                    ctx.roundRect(x, y, itemWidth - 15, 35, 8);
                    ctx.fill();
                }

                // Enhanced command indicator with glow
                ctx.shadowColor = categoryColor;
                ctx.shadowBlur = 8;
                ctx.fillStyle = categoryColor;
                ctx.beginPath();
                ctx.roundRect(x + 6, y + 10, 4, 16, 2);
                ctx.fill();
                ctx.shadowBlur = 0;

                // Command name with enhanced styling
                ctx.font = `bold 16px Arial`;
                ctx.textAlign = 'left';
                ctx.fillStyle = '#ffffff';
                ctx.fillText(this.truncateText(ctx, command.name, itemWidth - 30), x + 16, y + 17);

                // Command description with enhanced styling
                ctx.font = `12px Arial`;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                const description = command.description || 'No description';
                ctx.fillText(this.truncateText(ctx, description, itemWidth - 30), x + 16, y + 33);
            }
        }

        // Enhanced footer with stronger glass effect - landscape
        this.drawGlassPanel(ctx, 40, height - 60, width - 80, 45, 16, COLORS.GLASS_SECONDARY, COLORS.GLASS_BORDER, 2);

        // Add pagination indicator with enhanced styling - landscape
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = `16px Arial`;
        ctx.textAlign = 'center';
        const pageText = allCommandsInThisCategory.length > 0 ? `Page ${currentPage}/${totalPages}` : 'Page 1/1';
        ctx.fillText(`${pageText} | Use buttons below to navigate`, width / 2, height - 30);

        return canvas.toBuffer('image/png');
    }

    async generateCommandPage(command, prefix, client) {
        const width = 1200;
        const height = 600;
        const canvas = createCanvas(width, height);
        const ctx = canvas.getContext('2d');
        const fonts = this.getFonts();

        this.createBackgroundEffects(ctx, width, height);

        const categoryColor = this.getCategoryColor(command.category);
        
        // Enhanced header with glow effect - landscape
        this.drawGlassPanel(ctx, 40, 30, width - 80, 70, 16, COLORS.GLASS_PRIMARY, COLORS.GLASS_BORDER, 2);

        // Add decorative accent elements - landscape
        ctx.fillStyle = categoryColor;
        ctx.shadowColor = categoryColor;
        ctx.shadowBlur = 15;
        ctx.beginPath();
        ctx.roundRect(40, 30, width - 80, 4, [2, 2, 0, 0]);
        ctx.fill();
        ctx.beginPath();
        ctx.roundRect(40, 96, width - 80, 4, [0, 0, 2, 2]);
        ctx.fill();
        ctx.shadowBlur = 0;

        // Enhanced title with stronger glow - landscape
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 15;
        ctx.font = `bold 28px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        ctx.fillText(`COMMAND: ${command.name.toUpperCase()}`, width / 2, 70);
        ctx.shadowBlur = 0;

        // Category indicator with enhanced styling - landscape
        ctx.font = `bold 16px Arial`;
        ctx.fillStyle = categoryColor;
        ctx.textAlign = 'right';
        ctx.fillText(this.formatCategoryName(command.category), width - 60, 70);

        // Enhanced description panel with category color accent - landscape
        this.drawGlassPanel(ctx, 40, 120, width - 80, 100, 16, COLORS.GLASS_SECONDARY, categoryColor, 2);
        
        // Add decorative accent to description panel
        const descHeaderGradient = ctx.createLinearGradient(30, 130, 30, 160);
        descHeaderGradient.addColorStop(0, categoryColor);
        descHeaderGradient.addColorStop(1, 'rgba(30, 86, 240, 0.1)');
        ctx.fillStyle = descHeaderGradient;
        ctx.beginPath();
        ctx.roundRect(30, 130, width - 60, 30, [16, 16, 0, 0]);
        ctx.fill();

        // Description text with enhanced styling
        ctx.font = `18px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 3;
        const description = command.description || 'No description available.';

        const maxDescLineWidth = width - 100;
        let currentDescY = 175; // Adjusted for the header
        const descLineHeight = 22;
        const words = description.split(' ');
        let currentLine = '';

        for (const word of words) {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            if (ctx.measureText(testLine).width > maxDescLineWidth && currentLine !== '') {
                ctx.fillText(currentLine, width / 2, currentDescY);
                currentLine = word;
                currentDescY += descLineHeight;
            } else {
                currentLine = testLine;
            }
            if (currentDescY + descLineHeight > 130 + 130 - 30) break;
        }
        if (currentLine) ctx.fillText(currentLine, width / 2, currentDescY);
        ctx.shadowBlur = 0;

        // Usage text with enhanced styling
        currentDescY += descLineHeight + 5;
        if (currentDescY < 130 + 130 - 25) {
            ctx.font = `bold 20px Arial`;
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = categoryColor;
            ctx.shadowBlur = 5;
            const usageText = `Usage: ${prefix}${command.usage || command.name}`;
            ctx.fillText(this.truncateText(ctx, usageText, maxDescLineWidth), width / 2, currentDescY);
            ctx.shadowBlur = 0;
        }

        const detailStartY = 280;
        const detailPanelHeight = 180;

        // Left panel with enhanced styling
        this.drawGlassPanel(ctx, 30, detailStartY, width / 2 - 45, detailPanelHeight, 16, COLORS.GLASS_CONTENT, COLORS.GLASS_BORDER, 2);
        
        // Add decorative header to left panel
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.beginPath();
        ctx.roundRect(30, detailStartY, width / 2 - 45, 30, [16, 16, 0, 0]);
        ctx.fill();

        // Aliases section with enhanced styling
        ctx.font = `bold 20px Arial`;
        ctx.textAlign = 'left';
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 5;
        ctx.fillText('Aliases', 50, detailStartY + 35);
        ctx.shadowBlur = 0;

        // Aliases value with enhanced styling
        ctx.font = `16px Arial`;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        const aliases = command.aliases && command.aliases.length > 0 ? command.aliases.join(', ') : 'None';
        
        // Add subtle background for aliases
        if (aliases !== 'None') {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.beginPath();
            ctx.roundRect(50, detailStartY + 45, width / 2 - 145, 20, 4);
            ctx.fill();
        }
        
        ctx.fillStyle = aliases !== 'None' ? COLORS.ACCENT_HIGHLIGHT : 'rgba(255, 255, 255, 0.6)';
        ctx.fillText(this.truncateText(ctx, aliases, width / 2 - 100), 50, detailStartY + 60);

        // Cooldown section with enhanced styling
        ctx.font = `bold 20px Arial`;
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 5;
        ctx.fillText('Cooldown', 50, detailStartY + 100);
        ctx.shadowBlur = 0;

        // Cooldown value with enhanced styling
        ctx.font = `18px Arial`;
        const cooldown = command.cooldown || 0;
        ctx.fillStyle = cooldown > 0 ? COLORS.ACCENT_HIGHLIGHT : 'rgba(255, 255, 255, 0.6)';
        
        // Add subtle background for cooldown
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.beginPath();
        ctx.roundRect(50, detailStartY + 105, 100, 25, 4);
        ctx.fill();
        
        ctx.fillStyle = cooldown > 0 ? COLORS.ACCENT_HIGHLIGHT : 'rgba(255, 255, 255, 0.6)';
        ctx.fillText(`${cooldown} seconds`, 50, detailStartY + 125);

        // Right panel with enhanced styling
        this.drawGlassPanel(ctx, width / 2 + 15, detailStartY, width / 2 - 45, detailPanelHeight, 16, COLORS.GLASS_CONTENT, COLORS.GLASS_BORDER, 2);
        
        // Add decorative header to right panel
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.beginPath();
        ctx.roundRect(width / 2 + 15, detailStartY, width / 2 - 45, 30, [16, 16, 0, 0]);
        ctx.fill();

        // Category section with enhanced styling
        ctx.font = `bold 20px Arial`;
        ctx.textAlign = 'left';
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 5;
        ctx.fillText('Category', width / 2 + 35, detailStartY + 35);
        ctx.shadowBlur = 0;

        // Category value with enhanced styling
        ctx.font = `18px Arial`;
        ctx.fillStyle = categoryColor;
        ctx.shadowColor = categoryColor;
        ctx.shadowBlur = 8;
        ctx.fillText(this.formatCategoryName(command.category), width / 2 + 35, detailStartY + 60);
        ctx.shadowBlur = 0;

        // Requirements section with enhanced styling
        ctx.font = `bold 20px Arial`;
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 5;
        ctx.fillText('Requirements', width / 2 + 35, detailStartY + 95);
        ctx.shadowBlur = 0;

        // Requirements list with enhanced styling
        const requirements = this.getCommandRequirements(command);
        ctx.font = `14px Arial`;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        let reqY = detailStartY + 120;
        const reqLineHeight = 18;

        if (requirements.length > 0) {
            // Add subtle background for requirements
            ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
            ctx.beginPath();
            ctx.roundRect(width / 2 + 35, detailStartY + 105, width / 2 - 115, Math.min(requirements.length, 3) * reqLineHeight + 10, 4);
            ctx.fill();
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            for (let i = 0; i < Math.min(requirements.length, 3); i++) {
                if (reqY + reqLineHeight > detailStartY + detailPanelHeight - 10) break;
                
                // Add bullet point with accent color
                ctx.fillStyle = COLORS.ACCENT_HIGHLIGHT;
                ctx.beginPath();
                ctx.arc(width / 2 + 40, reqY - 5, 3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillText(`${this.truncateText(ctx, requirements[i], width / 2 - 100)}`, width / 2 + 50, reqY);
                reqY += reqLineHeight;
            }
            if (requirements.length > 3) {
                if (reqY + reqLineHeight <= detailStartY + detailPanelHeight - 10) {
                    ctx.fillStyle = COLORS.ACCENT_SECONDARY;
                    ctx.fillText(`+${requirements.length - 3} more`, width / 2 + 50, reqY);
                }
            }
        } else {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.fillText('None', width / 2 + 35, reqY);
        }

        // Enhanced footer with stronger glass effect
        this.drawGlassPanel(ctx, 30, height - 70, width - 60, 50, 16, COLORS.GLASS_SECONDARY, COLORS.GLASS_BORDER, 2);

        // Footer text with enhanced styling
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = `16px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(`Type "${prefix}help" or use the button to return to the main help menu`, width / 2, height - 40);

        return canvas.toBuffer('image/png');
    }

    async generateSearchResultsPage(searchTerm, allFilteredCommands, prefix, client, page = 1, itemsPerPage = 24) {
        const width = 1200;
        const height = 600;
        const canvas = createCanvas(width, height);
        const ctx = canvas.getContext('2d');
        const fonts = this.getFonts();

        this.createBackgroundEffects(ctx, width, height);

        // Enhanced header with search icon effect
        this.drawGlassPanel(ctx, 30, 30, width - 60, 80, 16, COLORS.GLASS_PRIMARY, COLORS.GLASS_BORDER, 2);
        
        // Add decorative search icon
        ctx.strokeStyle = COLORS.ACCENT_HIGHLIGHT;
        ctx.lineWidth = 3;
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 10;
        // Draw magnifying glass
        ctx.beginPath();
        ctx.arc(width/2 - 150, 75, 15, 0, Math.PI * 2);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(width/2 - 140, 85);
        ctx.lineTo(width/2 - 130, 95);
        ctx.stroke();
        ctx.shadowBlur = 0;

        // Enhanced title with stronger glow
        ctx.shadowColor = COLORS.ACCENT_GLOW;
        ctx.shadowBlur = 15;
        ctx.font = `bold 32px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        ctx.fillText('SEARCH RESULTS', width / 2, 75);
        ctx.shadowBlur = 0;

        // Search term and result count with enhanced styling
        ctx.font = `18px Arial`;
        ctx.fillStyle = COLORS.ACCENT_HIGHLIGHT;
        ctx.textAlign = 'right';
        ctx.fillText(`"${this.truncateText(ctx, searchTerm, 20)}" - ${allFilteredCommands.length} result${allFilteredCommands.length !== 1 ? 's' : ''}`, width - 50, 75);

        // Enhanced info panel with accent color
        this.drawGlassPanel(ctx, 30, 130, width - 60, 50, 16, COLORS.GLASS_SECONDARY, COLORS.GLASS_BORDER, 2);
        
        // Add subtle highlight to info panel
        const infoGradient = ctx.createLinearGradient(30, 130, width - 30, 130);
        infoGradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
        infoGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
        infoGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        ctx.fillStyle = infoGradient;
        ctx.fillRect(30, 130, width - 60, 50);

        // Info text with enhanced styling
        ctx.font = `18px Arial`;
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffffff';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 3;
        ctx.fillText(`Showing commands matching "${this.truncateText(ctx, searchTerm, 40)}"`, width / 2, 162);
        ctx.shadowBlur = 0;

        // Enhanced content panel with subtle pattern
        this.drawGlassPanel(ctx, 30, 200, width - 60, height - 280, 16, COLORS.GLASS_CONTENT, COLORS.GLASS_BORDER, 2);
        
        // Add subtle grid pattern inside content panel
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.lineWidth = 1;
        for (let x = 30; x < width - 30; x += 50) {
            ctx.beginPath();
            ctx.moveTo(x, 200);
            ctx.lineTo(x, height - 80);
            ctx.stroke();
        }

        allFilteredCommands.sort((a, b) => a.name.localeCompare(b.name));

        const totalPages = Math.ceil(allFilteredCommands.length / itemsPerPage);
        const currentPage = Math.max(1, Math.min(page, totalPages || 1));
        const startIndex = (currentPage - 1) * itemsPerPage;
        const commandsToDisplay = allFilteredCommands.slice(startIndex, startIndex + itemsPerPage);

        const columns = 3;
        const itemWidth = (width - 120) / columns;
        const itemHeight = 40;
        const startXOffset = 45;
        const startYOffset = 220;

        if (commandsToDisplay.length === 0 && currentPage === 1) {
            // Enhanced empty state with icon
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(width / 2, startYOffset + 60, 30, 0, Math.PI * 2);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(width / 2 + 21, startYOffset + 81);
            ctx.lineTo(width / 2 + 35, startYOffset + 95);
            ctx.stroke();
            
            ctx.font = `bold 22px Arial`;
            ctx.textAlign = 'center';
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 5;
            ctx.fillText('No commands found', width / 2, startYOffset + 130);
            ctx.shadowBlur = 0;

            ctx.font = `18px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.fillText('Try a different search term', width / 2, startYOffset + 160);
        } else {
            // Add header row with subtle styling
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.fillRect(startXOffset, startYOffset - 25, width - 90, 20);
            ctx.font = `bold 14px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.textAlign = 'left';
            ctx.fillText('COMMAND', startXOffset + 16, startYOffset - 10);
            ctx.fillText('CATEGORY', startXOffset + 16 + itemWidth, startYOffset - 10);
            
            // Highlight search term matches
            const searchTermLower = searchTerm.toLowerCase();
            
            for (let i = 0; i < commandsToDisplay.length; i++) {
                const command = commandsToDisplay[i];
                const col = i % columns;
                const row = Math.floor(i / columns);
                const x = startXOffset + (col * itemWidth);
                const y = startYOffset + (row * itemHeight);

                if (y + itemHeight > (200 + height - 280 - 15)) break;

                // Enhanced command item with hover-like effect
                if (i % 2 === 0) {
                    // Create subtle gradient for even rows
                    const rowGradient = ctx.createLinearGradient(x, y, x + itemWidth - 15, y);
                    rowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.08)');
                    rowGradient.addColorStop(1, 'rgba(255, 255, 255, 0.02)');
                    ctx.fillStyle = rowGradient;
                    ctx.beginPath();
                    ctx.roundRect(x, y, itemWidth - 15, 35, 8);
                    ctx.fill();
                } else {
                    // Subtle background for odd rows
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                    ctx.beginPath();
                    ctx.roundRect(x, y, itemWidth - 15, 35, 8);
                    ctx.fill();
                }

                // Enhanced category indicator with glow
                const categoryColor = this.getCategoryColor(command.category);
                ctx.shadowColor = categoryColor;
                ctx.shadowBlur = 8;
                ctx.fillStyle = categoryColor;
                ctx.beginPath();
                ctx.roundRect(x + 6, y + 10, 4, 16, 2);
                ctx.fill();
                ctx.shadowBlur = 0;

                // Command name with enhanced styling
                ctx.font = `bold 16px Arial`;
                ctx.textAlign = 'left';
                ctx.fillStyle = '#ffffff';
                
                // Highlight if command name contains search term
                if (command.name.toLowerCase().includes(searchTermLower)) {
                    ctx.fillStyle = COLORS.ACCENT_HIGHLIGHT;
                }
                
                ctx.fillText(this.truncateText(ctx, command.name, itemWidth - 50), x + 16, y + 17);

                // Category name with enhanced styling
                const categoryName = this.formatCategoryName(command.category);
                ctx.font = `12px Arial`;
                ctx.fillStyle = categoryColor;
                ctx.fillText(this.truncateText(ctx, categoryName, itemWidth - 30), x + 16, y + 33);
            }
        }

        // Enhanced footer with stronger glass effect
        this.drawGlassPanel(ctx, 30, height - 70, width - 60, 50, 16, COLORS.GLASS_SECONDARY, COLORS.GLASS_BORDER, 2);

        // Footer text with enhanced styling
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = `16px Arial`;
        ctx.textAlign = 'center';
        const pageText = allFilteredCommands.length > 0 ? `Page ${currentPage}/${totalPages}` : 'Page 1/1';
        ctx.fillText(`${pageText} | Use buttons below to navigate`, width / 2, height - 40);

        return canvas.toBuffer('image/png');
    }

    getCommandRequirements(command) {
        const requirements = [];

        if (command.ownerOnly || command.OwnerOnly || command.category?.toLowerCase() === 'owner') {
            requirements.push('Bot Owner Only');
        } else if (command.category?.toLowerCase() === 'developer') {
            requirements.push('Bot Developer Only');
        }

        if (command.permissions && command.permissions.length > 0) {
            command.permissions.forEach(perm => {
                requirements.push(`Requires ${this.formatPermission(perm)}`);
            });
        }

        if (command.voiceRequired) {
            requirements.push('Must be in a voice channel');
        }

        if (command.sameVoiceRequired) {
            requirements.push('Must be in same voice channel as bot');
        }

        if (command.playerRequired) {
            requirements.push('Music player must be active');
        }

        if (command.playingRequired) {
            requirements.push('Music must be playing');
        }

        if (command.customRequirements && command.customRequirements.length > 0) {
            command.customRequirements.forEach(req => {
                requirements.push(req);
            });
        }

        return requirements;
    }

    formatPermission(permission) {
        return permission
            .toLowerCase()
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
}

module.exports = { HelpCanvas };
