const {
    SlashCommandSubcommandBuilder,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');
const config = require('../../../../../config/config.js');
const events = require('events');

// Active games map
const activeMatchPairsGames = new Map();

// MatchPairs Class
class MatchPairs extends events {
    constructor(options = {}) {
        if (!options.isSlashGame) options.isSlashGame = false;
        if (!options.message) throw new TypeError('NO_MESSAGE: No message option was provided.');
        if (typeof options.message !== 'object') throw new TypeError('INVALID_MESSAGE: message option must be an object.');
        if (typeof options.isSlashGame !== 'boolean') throw new TypeError('INVALID_COMMAND_TYPE: isSlashGame option must be a boolean.');

        if (!options.embed) options.embed = {};
        if (!options.embed.title) options.embed.title = 'Match Pairs';
        if (!options.embed.color) options.embed.color = '#5865F2';
        if (!options.embed.description) options.embed.description = '**Click on the buttons to match emojis with their pairs.**';

        if (!options.timeoutTime) options.timeoutTime = 60000;
        if (!options.emojis) options.emojis = ['🍉', '🍇', '🍊', '🍋', '🥭', '🍎', '🍏', '🥝', '🥥', '🍓', '🍒', '🫐', '🍍', '🍅', '🍐', '🥔', '🌽', '🥕', '🥬', '🥦'];
        if (!options.winMessage) options.winMessage = '**You won the Game! You turned a total of `{tilesTurned}` tiles.**';
        if (!options.loseMessage) options.loseMessage = '**You lost the Game! You turned a total of `{tilesTurned}` tiles.**';

        super();
        this.options = options;
        this.message = options.message;
        this.emojis = this.shuffleArray(options.emojis).slice(0, 12);
        this.emojis.push(...this.emojis, '🃏');
        this.emojis = this.shuffleArray(this.emojis);
        this.remainingPairs = 12;
        this.components = [];
        this.selected = null;
        this.tilesTurned = 0;
        this.length = 5;
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    async sendMessage(content) {
        return this.options.isSlashGame ? this.message.editReply(content) : this.message.channel.send(content);
    }

    async startGame() {
        if (this.options.isSlashGame || !this.message.author) {
            if (!this.message.deferred) await this.message.deferReply().catch(() => {});
            this.message.author = this.message.user;
            this.options.isSlashGame = true;
        }
        this.components = this.getComponents();
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription(this.options.embed.description)
            .setAuthor({ name: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        const msg = await this.sendMessage({ embeds: [embed], components: this.components });
        this.handleButtons(msg);
    }

    getComponents() {
        const components = [];
        for (let y = 0; y < this.length; y++) {
            const row = new ActionRowBuilder();
            for (let x = 0; x < this.length; x++) {
                row.addComponents(new ButtonBuilder().setStyle(ButtonStyle.Secondary).setLabel('\u200b').setCustomId(`matchpairs_${x}_${y}`));
            }
            components.push(row);
        }
        return components;
    }

    getPairEmoji(emoji) {
        const res = [];
        for (let y = 0; y < this.length; y++) {
            for (let x = 0; x < this.length; x++) {
                const idx = y * this.length + x;
                if (this.emojis[idx] === emoji) res.push({ x, y, id: idx });
            }
        }
        return res;
    }

    disableButtons(components) {
        for (const row of components) {
            for (const btn of row.components) ButtonBuilder.from(btn).setDisabled(true);
        }
        return components;
    }

    async gameOverWithInteraction(interaction, won) {
        const Game = { player: this.message.author, tilesTurned: this.tilesTurned, remainingPairs: this.remainingPairs };
        this.emit('gameOver', { result: won ? 'win' : 'lose', ...Game });
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription((won ? this.options.winMessage : this.options.loseMessage).replace('{tilesTurned}', this.tilesTurned))
            .setAuthor({ name: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        await interaction.update({ embeds: [embed], components: this.disableButtons(this.components) });
    }

    handleButtons(msg) {
        const collector = msg.createMessageComponentCollector({ idle: this.options.timeoutTime });
        collector.on('collect', async (btn) => {
            try {
                if (btn.user.id !== this.message.author.id) {
                    if (this.options.playerOnlyMessage) {
                        await btn.reply({ content: this.formatMessage(this.options, 'playerOnlyMessage'), ephemeral: true });
                    } else await btn.deferUpdate();
                    return;
                }
                const x = parseInt(btn.customId.split('_')[1]);
                const y = parseInt(btn.customId.split('_')[2]);
                const idx = y * this.length + x;
                const emoji = this.emojis[idx];
                const btnObj = this.components[y].components[x];
                this.tilesTurned++;

                if (!this.selected) {
                    this.selected = { x, y, id: idx };
                    btnObj.setEmoji(emoji).setStyle(ButtonStyle.Primary);
                    if (btnObj.data.label) delete btnObj.data.label;
                } else if (this.selected.id === idx) {
                    this.selected = null;
                    if (btnObj.data.emoji) delete btnObj.data.emoji;
                    btnObj.setStyle(ButtonStyle.Secondary).setLabel('\u200b');
                } else {
                    const prevBtn = this.components[this.selected.y].components[this.selected.x];
                    const match = (emoji === this.emojis[this.selected.id] || emoji === '🃏' || this.emojis[this.selected.id] === '🃏');
                    if (emoji === '🃏' || this.emojis[this.selected.id] === '🃏') {
                        const joker = emoji === '🃏' ? { x, y, id: idx } : this.selected;
                        const pair = this.getPairEmoji(this.emojis[joker.id === idx ? this.selected.id : idx]).find(b => b.id !== joker.id);
                        if (pair) {
                            const pairBtn = this.components[pair.y].components[pair.x];
                            pairBtn.setEmoji(this.emojis[pair.id]).setStyle(ButtonStyle.Success).setDisabled(true);
                            if (pairBtn.data.label) delete pairBtn.data.label;
                        }
                    }
                    btnObj.setEmoji(emoji).setStyle(match ? ButtonStyle.Success : ButtonStyle.Danger).setDisabled(match);
                    prevBtn.setEmoji(this.emojis[this.selected.id]).setStyle(match ? ButtonStyle.Success : ButtonStyle.Danger).setDisabled(match);
                    if (btnObj.data.label) delete btnObj.data.label;
                    if (prevBtn.data.label) delete prevBtn.data.label;
                    if (!match) {
                        await btn.update({ components: this.components });
                        setTimeout(async () => {
                            try {
                                if (btnObj.data.emoji) delete btnObj.data.emoji;
                                if (prevBtn.data.emoji) delete prevBtn.data.emoji;
                                btnObj.setStyle(ButtonStyle.Secondary).setLabel('\u200b').setDisabled(false);
                                prevBtn.setStyle(ButtonStyle.Secondary).setLabel('\u200b').setDisabled(false);
                                await msg.edit({ components: this.components });
                            } catch {}
                        }, 1000);
                        this.selected = null;
                        return;
                    }
                    this.remainingPairs--;
                    this.selected = null;
                    if (this.remainingPairs === 0) {
                        await this.gameOverWithInteraction(btn, true);
                        return collector.stop();
                    }
                }
                await btn.update({ components: this.components });
            } catch (e) { console.error(e); collector.stop(); }
        });
        collector.on('end', async (_, reason) => {
            if (reason === 'idle') {
                const Game = { player: this.message.author, tilesTurned: this.tilesTurned, remainingPairs: this.remainingPairs };
                this.emit('gameOver', { result: 'lose', ...Game });
            }
        });
    }

    formatMessage(opts, key) {
        let txt = opts[key];
        txt = txt.replace('{player}', `<@!${opts.message.author.id}>`);
        txt = txt.replace('{player.tag}', opts.message.author.tag);
        txt = txt.replace('{player.username}', opts.message.author.username);
        return txt;
    }
}

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('matchpairs')
        .setDescription('Play the Match Pairs memory game'),

    async execute(interaction) {
        try {
            const game = new MatchPairs({
                message: interaction,
                isSlashGame: true,
                embed: { title: '🧩 Match Pairs', color: config.EmbedConfig.embedColor, description: 'Match all pairs.' },
                emojis: ['🍉', '🍇', '🍊', '🍋', '🥭', '🍎', '🍏', '🥝', '🥥', '🍓', '🍒', '🫐', '🍍', '🍅', '🍐', '🥔', '🌽', '🥕', '🥬', '🥦'],
                timeoutTime: 300_000,
                winMessage: '**You won! Turned `{tilesTurned}` tiles.**',
                loseMessage: '**You lost! Turned `{tilesTurned}` tiles.**',
                playerOnlyMessage: 'Only {player} can use these buttons.'
            });
            activeMatchPairsGames.set(interaction.user.id, game);
            game.on('gameOver', () => activeMatchPairsGames.delete(interaction.user.id));
            await game.startGame();
        } catch (e) {
            console.error(e);
            if (!interaction.replied) await interaction.reply({ content: '❌ Error starting match pairs.', ephemeral: true });
        }
    },

    async handleButtonInteraction(interaction) {
        // MatchPairs uses collectors, so buttons are handled internally
        // This method exists for consistency but doesn't need to do anything
        return;
    }
};
