const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('rules')
        .setDescription('Display the server rules.'),

    name: 'rules',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: 'rules',
    description: 'Displays the server rules',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
	
    async execute(interaction) {
        const rulesEmbed = new EmbedBuilder()
            .setColor('#6F2DA8') // Purple color reminiscent of Solo Leveling
            .setDescription(`# RankBreaker Rules\n\n## Ⅰ ↠ HUNTER'S CODE OF CONDUCT\n> Maintain a respectful and welcoming environment. Disrespect, harassment, or toxic behavior towards fellow hunters will not be tolerated. Remember, even the weakest E-rank hunter deserves respect.\n\n## Ⅱ ↠ FORBIDDEN CONTENT\n> NSFW content is strictly forbidden as if sealed by the System. This includes profile pictures, banners, and discussions. Violators will be immediately banished from this realm. The Shadow Monarch shows no mercy for those who share explicit content.\n\n## Ⅲ ↠ HUNTER\'S EQUALITY\n> Racism, sexism, homophobia, or any discriminatory language is forbidden. The System grants equal opportunity to all hunters regardless of background. Bypassing or hinting at slurs will result in punishment as severe as a high-rank dungeon break.\n\n## Ⅳ ↠ RANK REQUESTS\n> Do not repeatedly ask for roles or ranks. True hunters earn their ranks through dedication, not begging. Guild Masters and S-Rank hunters shouldn\'t be bothered with trivial requests.\n\n## Ⅴ ↠ FORBIDDEN TOPICS\n> Jokes or references about sensitive topics such as rape, suicide, and similar dark magic are strictly prohibited. Such darkness attracts unwanted shadows that even the Shadow Monarch cannot control.\n\n## Ⅵ ↠ DUNGEON ETIQUETTE\n> Avoid flooding chats with excessive messages, gifs, or emojis as if you were setting off traps in a dungeon. Use each channel for its intended purpose. The right skills should be used in the right battles.\n\n## Ⅶ ↠ GUILD RECRUITMENT\n> Advertising or self-promotion without the approval of Guild Masters is forbidden. Even the most powerful S-Rank Hunter needs permission before recruiting within another\'s territory.\n\n## Ⅷ ↠ RESPECTING AUTHORITY\n> If you receive a warning from a Guild Master, accept it with dignity. Do not argue or challenge their authority. If you believe a ruling is unjust, request an audience with the Shadow Monarch privately.\n\n## Ⅸ ↠ COMMON TONGUE\n> The common language of this guild is English. Using other languages in general channels is forbidden, as not all hunters or Guild Masters can understand foreign incantations.\n\n## Ⅹ ↠ EXPLOITATION IS FORBIDDEN\n> Exploiting, scripting, injecting, or using any third-party tools to gain unfair advantage is considered black magic. The use, promotion, or distribution of exploits will result in a permanent banishment from the guild. No excuses. No second chances. Only ruin awaits the corrupted.\n\n## Ⅺ ↠ ROBLOX DECREE\n> All hunters must also follow the Roblox Terms of Service without exception. Any attempts to bypass Roblox's rules—be it through off-platform transactions, inappropriate content, or manipulation of in-game systems—will summon instant expulsion. The System aligns with Roblox’s law above all else.\n\n## Ⅻ ↠ SYSTEM'S LAW\n> All hunters must abide by Discord's Terms of Service & Community Guidelines. Consider your actions before posting, as even the Shadow Monarch must bow to the greater powers that govern this realm.`)
            .setImage('https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExcHk4Z2l1bXoxenZ2dXlnNGhlNnliamVia2FvM212ZXVnYjlwdW53aiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/ngPwiC0nGw5db9IQ24/giphy.gif')
            .setFooter({ text: 'RankBreaker | discord.gg/M6e3PGRtd3' })
            .setTimestamp();

        await interaction.channel.send({ embeds: [rulesEmbed] });
    },
};