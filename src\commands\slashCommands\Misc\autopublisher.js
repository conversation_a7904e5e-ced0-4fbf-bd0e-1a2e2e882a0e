const {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ChannelType,
    PermissionFlagsBits
} = require('discord.js');
const configService = require('../../../services/configService');
const chalk = require('chalk');
const path = require('path');

const cmdname = path.parse(__filename).name; // file name
const folderName = path.basename(__dirname); // folder name

// Setup Emojis - Centralized emoji definitions for the autopublisher system
const AUTOPUBLISHER_EMOJIS = {
    // Status emojis
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    ENABLED: '<:enabled_RB:1384533989444030655>',
    DISABLED: '<:disabled_RB:1384534072675664002>',
    VALID: '✅',
    INVALID: '❌',

    // Feature emojis
    AUTOPUBLISHER: '📢',
    CHANNELS: '📢',
    ANNOUNCEMENT: '📣',

    // Action emojis
    ENABLE: '1384575597656674446',
    DISABLE: '1384575688039727244',
    BACK: '🔙',
    CANCEL: '❌',
    ADD_CHANNEL: '➕',
    REMOVE_CHANNEL: '➖',

    // Process emojis
    LOADING: '⏳',
    PROGRESS: '🔄',

    // Management emojis
    MANAGE: '🔧',
    DELETE: '🗑️',
    SETUP: '🧱',
    TARGET: '🎯',

    // Special action emojis
    FINISH: '✅',
    CONFIRM: '✅',

    // Additional emojis
    NOT_SET: '❌',
    CONFIGURED: '✅',
    NOT_CONFIGURED: '❌',
    TIMEOUT_WARNING: '⏰',
    INVALID_FORMAT: '❌',
    CHANNEL_NOT_FOUND: '❌',
    INVALID_CHANNEL_TYPE: '❌',
    CHANNEL_SELECTED: '✅',
    PROCESSING: '⏳',
    COMPLETE: '✅'
};

// Session management for autopublisher setup process
const autopublisherSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds

module.exports = {
    data: new SlashCommandBuilder()
        .setName('autopublisher')
        .setDescription('Configure automatic message publishing for announcement channels')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),
    name: cmdname,
    category: folderName,
    aliases: [],
    cooldown: 10,
    usage: '/autopublisher',
    description: 'Configure automatic message publishing for announcement channels',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'guild',
    
    async execute(interaction) {
        try {
            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return await interaction.reply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} You need the "Manage Server" permission to use this command.`,
                    ephemeral: true
                });
            }

// Check for existing session
const existingSession = autopublisherSessions.get(interaction.user.id);
if (existingSession) {
    // Calculate remaining time
    const currentTime = Date.now();
    const sessionStartTime = existingSession.startTime;
    const elapsedTime = currentTime - sessionStartTime;
    const remainingTime = SESSION_TIMEOUT - elapsedTime;
    
    if (remainingTime > 0) {
        const remainingMinutes = Math.ceil(remainingTime / (60 * 1000));
        return await interaction.reply({
            content: `${AUTOPUBLISHER_EMOJIS.WARNING} You already have an active autopublisher session. Please cancel it first.\n\nOr wait **${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}** and try again.`,
            ephemeral: true
        });
    } else {
        // Session has expired, remove it
        autopublisherSessions.delete(interaction.user.id);
    }
}

            // Show main autopublisher interface
            await this.showAutopublisherMain(interaction);

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error in autopublisher command:`), error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle(`${AUTOPUBLISHER_EMOJIS.ERROR} Autopublisher Error`)
                .setDescription('An error occurred during autopublisher setup. Please try again or contact support.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async showAutopublisherMain(interaction) {
        // Get current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const autopublisherConfig = currentConfig?.autopublisher || {
            enabled: true,
            channels: {}
        };

        // Get configured channels
        const configuredChannels = Object.entries(autopublisherConfig.channels || {});
        const channelCount = configuredChannels.length;

        // Build channel status display
        let channelStatusText = '';
        if (channelCount === 0) {
            channelStatusText = `${AUTOPUBLISHER_EMOJIS.NOT_CONFIGURED} No channels configured`;
        } else {
            channelStatusText = configuredChannels.map(([channelId, config], index) => {
                const channel = interaction.guild.channels.cache.get(channelId);
                const channelName = channel ? `<#${channelId}>` : `~~Unknown Channel~~`;
                const status = config.enabled ? 
                    `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled**` : 
                    `${AUTOPUBLISHER_EMOJIS.DISABLED} **Disabled**`;
                return `**${index + 1}.** ${channelName} - ${status}`;
            }).join('\n');
        }

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.AUTOPUBLISHER} Auto Publisher Configuration`)
            .setDescription('Configure automatic message publishing for announcement channels.')
            .addFields([
                {
                    name: 'Current Status',
                    value: autopublisherConfig.enabled ? 
                        `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled**` : 
                        `${AUTOPUBLISHER_EMOJIS.DISABLED} **Disabled**`,
                    inline: true
                },
                {
                    name: 'Configured Channels',
                    value: `${channelCount}/3 channels configured`,
                    inline: true
                },
                {
                    name: '\u200b',
                    value: '\u200b',
                    inline: true
                },
                {
                    name: 'Channel Configuration',
                    value: channelStatusText,
                    inline: false
                }
            ])
            .setFooter({ text: 'You can configure up to 3 announcement channels' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('autopublisher_add_channel')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.ADD_CHANNEL} Add Channel`)
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(channelCount >= 3),
                new ButtonBuilder()
                    .setCustomId('autopublisher_manage_channels')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.MANAGE} Manage Channels`)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(channelCount === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('autopublisher_toggle_global')
                    .setLabel(autopublisherConfig.enabled ? 'Disable Auto Publisher' : 'Enable Auto Publisher')
                    .setEmoji(autopublisherConfig.enabled ? AUTOPUBLISHER_EMOJIS.DISABLE : AUTOPUBLISHER_EMOJIS.ENABLE)
                    .setStyle(autopublisherConfig.enabled ? ButtonStyle.Danger : ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('autopublisher_cancel')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.CANCEL} Cancel`)
                    .setStyle(ButtonStyle.Secondary)
            );

        // Use update if interaction is already replied/deferred, otherwise reply
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row1, row2] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row1, row2], ephemeral: true });
        }

        // Create session
        this.createSession(interaction.user.id, {
            step: 'main',
            guildId: interaction.guild.id,
            channelId: interaction.channel.id,
            startTime: Date.now()
        });
    },

    async showAddChannel(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.ADD_CHANNEL} Add Announcement Channel`)
            .setDescription('Provide the channel ID for auto publishing:')
            .addFields([
                {
                    name: 'Instructions',
                    value: '1. **Send the Channel ID** in this chat\n2. You can get the Channel ID by right-clicking on a channel and selecting "Copy Channel ID"\n3. The channel must be an **Announcement Channel** (News Channel)\n4. The bot will automatically delete your message and update the configuration\n\n*You have 60 seconds to respond.*',
                    inline: false
                },
                {
                    name: 'Requirements',
                    value: '• Channel must be an Announcement/News channel\n• Bot must have "Manage Messages" permission in the channel\n• Maximum 3 channels can be configured',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('autopublisher_back')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row]
        });

        this.updateSession(interaction.user.id, { 
            step: 'adding_channel',
            pendingChannelInput: {
                channelId: interaction.channel.id,
                userId: interaction.user.id
            }
        });

        // Create a collector to wait for the user's channel input
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 60000, // 1 minute timeout
            max: 1
        });

        collector.on('collect', async (message) => {
            try {
                await this.processChannelInput(message, interaction);
            } catch (error) {
                console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing channel input:`), error);
                await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
            }
        });

        collector.on('end', (collected) => {
            if (collected.size === 0) {
                interaction.followUp({
                    content: `${AUTOPUBLISHER_EMOJIS.TIMEOUT_WARNING} Channel input timed out. Please try again.`,
                    ephemeral: true
                }).catch(console.error);
            }
        });
    },

    async showManageChannels(interaction) {
        //console.log(chalk.blue(`🔍 DEBUG: showManageChannels called`));
        
        // Get current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const autopublisherConfig = currentConfig?.autopublisher || { channels: {} };
        const configuredChannels = Object.entries(autopublisherConfig.channels || {});
        
        // console.log(chalk.blue(`🔍 DEBUG: Current config channels:`));
        configuredChannels.forEach(([channelId, config]) => {
            console.log(chalk.blue(`  - Channel ${channelId}: enabled=${config.enabled}`));
        });

        if (configuredChannels.length === 0) {
            return await interaction.update({
                content: `${AUTOPUBLISHER_EMOJIS.ERROR} No channels configured to manage.`,
                embeds: [],
                components: []
            });
        }

        // Build channel list for embed
        const channelList = configuredChannels.map(([channelId, config], index) => {
            const channel = interaction.guild.channels.cache.get(channelId);
            const channelName = channel ? `<#${channelId}>` : `~~Unknown Channel (${channelId})~~`;
            const status = config.enabled ? 
                `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled**` : 
                `${AUTOPUBLISHER_EMOJIS.DISABLED} **Disabled**`;
            return `**Channel ${index + 1}:** ${channelName} - ${status}`;
        }).join('\n');

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.MANAGE} Manage Autopublisher Channels`)
            .setDescription('Manage your configured autopublisher channels:')
            .addFields([
                {
                    name: 'Configured Channels',
                    value: channelList,
                    inline: false
                }
            ])
            .setTimestamp();

        // For single channel, show the new layout
        if (configuredChannels.length === 1) {
            const [channelId, config] = configuredChannels[0];

            // Create the new layout with three buttons in a row
            const row1 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_channel_display')
                        .setLabel(`Channel 1:`)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true),
                    new ButtonBuilder()
                        .setCustomId('autopublisher_change_channel')
                        .setLabel('Change Channel')
                        .setStyle(ButtonStyle.Primary),
                    (() => {
                        //console.log(chalk.blue(`🔍 DEBUG: Creating button for channel ${channelId} - enabled: ${config.enabled}, label: "${config.enabled ? 'Disable for This Channel' : 'Enable for This Channel'}", style: ${config.enabled ? 'Danger' : 'Success'}`));
                        return new ButtonBuilder()
                            .setCustomId(`autopublisher_toggle_${channelId}`)
                            .setLabel(config.enabled ? 'Disable for This Channel' : 'Enable for This Channel')
                            .setStyle(config.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                            .setEmoji(config.enabled ? AUTOPUBLISHER_EMOJIS.DISABLE : AUTOPUBLISHER_EMOJIS.ENABLE);
                    })()
                );

            // Add back button
            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_back')
                        .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row1, row2] });
        } else {
            // For multiple channels, use the same format as single channel but with multiple rows
            const components = [];
            
            // Create a row for each channel with the same 3-button layout
            configuredChannels.forEach(([channelId, config], index) => {
                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`autopublisher_channel_display_${index}`)
                            .setLabel(`Channel ${index + 1}:`)
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(true),
                        new ButtonBuilder()
                            .setCustomId(`autopublisher_change_channel_${channelId}`)
                            .setLabel('Change Channel')
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId(`autopublisher_toggle_${channelId}`)
                            .setLabel(config.enabled ? 'Disable for This Channel' : 'Enable for This Channel')
                            .setStyle(config.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                            .setEmoji(config.enabled ? AUTOPUBLISHER_EMOJIS.DISABLE : AUTOPUBLISHER_EMOJIS.ENABLE)
                    );
                components.push(row);
            });

            // Add control row
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_back')
                        .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );
            components.push(controlRow);

            await interaction.update({ embeds: [embed], components });
        }

        this.updateSession(interaction.user.id, { step: 'managing_channels' });
    },

    async processChannelInput(message, originalInteraction) {
        try {
            const channelInput = message.content.trim();
            let channelId = null;

            // Parse channel mention or ID
            const mentionMatch = channelInput.match(/^<#(\d+)>$/);
            if (mentionMatch) {
                channelId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(channelInput)) {
                channelId = channelInput;
            }
            else {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a channel mention (#channel-name) or a numeric channel ID.`);
            }

            // Validate the channel exists and is an announcement channel
            const channel = message.guild.channels.cache.get(channelId);
            if (!channel) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.CHANNEL_NOT_FOUND} Channel not found. Please make sure the channel exists in this server and try again.`);
            }

            if (channel.type !== ChannelType.GuildAnnouncement) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_CHANNEL_TYPE} Invalid channel type. Please provide an **Announcement Channel** (News Channel) for auto publishing.`);
            }

            // Check if channel is already configured
            const currentConfig = await configService.getServerConfig(message.guild.id);
            const autopublisherConfig = currentConfig?.autopublisher || { channels: {} };
            
            if (autopublisherConfig.channels && autopublisherConfig.channels[channelId]) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.WARNING} This channel is already configured for auto publishing.`);
            }

            // Check if we've reached the limit
            const channelCount = Object.keys(autopublisherConfig.channels || {}).length;
            if (channelCount >= 3) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} Maximum of 3 channels can be configured for auto publishing.`);
            }

            // Save the channel assignment
            await this.saveChannelAssignment(message, channelId, channel.name, originalInteraction);

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing channel input:`), error);
            await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
        }
    },

    async saveChannelAssignment(message, channelId, channelName, originalInteraction) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }
            
            // Get current configuration
            let config = await configService.getServerConfig(message.guild.id);
            if (!config) {
                // Create new config with explicit field ordering to match schema
                config = {};
                config.serverId = message.guild.id;
                config.name = message.guild.name;
                config.autopublisher = {
                    enabled: true,
                    channels: {}
                };
                config.dungeonAlert = { enabled: false };
                config.worldBossAlert = { enabled: false };
                config.infernalAlert = { enabled: false };
            } else {
                if (!config.autopublisher) {
                    config.autopublisher = {
                        enabled: true,
                        channels: {}
                    };
                }
            }

            // Create a deep copy to ensure proper saving (fixes MongoDB document modification issue)
            const newConfig = JSON.parse(JSON.stringify(config));
            
            // Add the new channel
            if (!newConfig.autopublisher.channels) {
                newConfig.autopublisher.channels = {};
            }
            
            newConfig.autopublisher.channels[channelId] = {
                enabled: true,
                name: channelName,
                addedAt: new Date()
            };

            // Save to database
            await configService.saveServerConfig(message.guild.id, newConfig);
            
            console.log(chalk.green(`✅ Channel configuration saved for user ${message.author.id}, updating interface`));
            
            // Show success and return to main interface
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${AUTOPUBLISHER_EMOJIS.SUCCESS} Channel Added Successfully`)
                .setDescription(`Successfully added <#${channelId}> to auto publisher configuration!`)
                .addFields([
                    {
                        name: 'Added Channel',
                        value: `<#${channelId}> ${channelName ? `(${channelName})` : ''}`,
                        inline: false
                    },
                    {
                        name: 'Status',
                        value: `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled** - Messages in this channel will be automatically published`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const successRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_back')
                        .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back to Main`)
                        .setStyle(ButtonStyle.Primary)
                );

            // Update the original interaction
            await originalInteraction.editReply({ 
                embeds: [successEmbed], 
                components: [successRow] 
            });
            
            console.log(chalk.green(`✅ Interface updated successfully with channel configuration`));
            
        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error saving channel assignment:`), error);
            try {
                const errorMessage = await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while saving the channel assignment. Please try again.`);
                
                // Auto-delete the error message after 10 seconds
                setTimeout(async () => {
                    try {
                        await errorMessage.delete();
                    } catch (deleteError) {
                        console.log(chalk.yellow(`⚠️ Could not delete error message: ${deleteError.message}`));
                    }
                }, 10000);
            } catch (replyError) {
                console.log(chalk.red(`❌ Could not send error reply: ${replyError.message}`));
            }
        }
    },

    async toggleGlobalAutopublisher(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            if (!config) {
                // Create new config with explicit field ordering to match schema
                config = {};
                config.serverId = interaction.guild.id;
                config.name = interaction.guild.name;
                config.autopublisher = {
                    enabled: true,
                    channels: {}
                };
                config.dungeonAlert = { enabled: false };
                config.worldBossAlert = { enabled: false };
                config.infernalAlert = { enabled: false };
            }

            if (!config.autopublisher) {
                config.autopublisher = {
                    enabled: true,
                    channels: {}
                };
            }

            // Toggle the enabled state
            const newState = !config.autopublisher.enabled;
            
            // Create a deep copy to ensure proper saving (fixes MongoDB document modification issue)
            const newConfig = JSON.parse(JSON.stringify(config));
            newConfig.autopublisher.enabled = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, newConfig);

            // Show updated main interface with instant feedback (like setup.js does)
            await this.showAutopublisherMain(interaction);

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error toggling global autopublisher:`), error);
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} Failed to toggle auto publisher. Please try again.`,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} Failed to toggle auto publisher. Please try again.`,
                    ephemeral: true
                });
            }
        }
    },

    async toggleChannelAutopublisher(interaction, channelId) {
        try {
            console.log(chalk.blue(`🔍 DEBUG: toggleChannelAutopublisher called with channelId: "${channelId}"`));
            
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            // console.log(chalk.blue(`🔍 DEBUG: Config exists: ${!!config}`));
            // console.log(chalk.blue(`🔍 DEBUG: Autopublisher exists: ${!!config?.autopublisher}`));
            // console.log(chalk.blue(`🔍 DEBUG: Channels exists: ${!!config?.autopublisher?.channels}`));
            
            if (config?.autopublisher?.channels) {
                //console.log(chalk.blue(`🔍 DEBUG: Available channel IDs in database:`));
                Object.keys(config.autopublisher.channels).forEach(id => {
                    console.log(chalk.blue(`  - "${id}" (type: ${typeof id}, length: ${id.length})`));
                });
               // console.log(chalk.blue(`🔍 DEBUG: Looking for channelId: "${channelId}" (type: ${typeof channelId}, length: ${channelId.length})`));
               // console.log(chalk.blue(`🔍 DEBUG: Channel found: ${!!config.autopublisher.channels[channelId]}`));
            }
            
            if (!config?.autopublisher?.channels?.[channelId]) {
               // console.log(chalk.red(`❌ DEBUG: Channel configuration not found for channelId: "${channelId}"`));
                return await interaction.reply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} Channel configuration not found. Debug: channelId="${channelId}"`,
                    ephemeral: true
                });
            }

            // Toggle the channel's enabled state
            const currentState = config.autopublisher.channels[channelId].enabled;
           // console.log(chalk.blue(`🔍 DEBUG: Current state: ${currentState}, toggling to: ${!currentState}`));
            
            // Create a deep copy to ensure proper saving
            const newConfig = JSON.parse(JSON.stringify(config));
            newConfig.autopublisher.channels[channelId].enabled = !currentState;
            
           // console.log(chalk.blue(`🔍 DEBUG: After toggle - newConfig enabled: ${newConfig.autopublisher.channels[channelId].enabled}`));

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, newConfig);
           // console.log(chalk.green(`✅ DEBUG: Configuration saved successfully`));

            // Clear cache to ensure fresh data
            configService.clearCache();
           // console.log(chalk.green(`✅ DEBUG: Cache cleared`));

            // Show updated manage channels interface with instant feedback (like setup.js does)
            await this.showManageChannels(interaction);
           // console.log(chalk.green(`✅ DEBUG: showManageChannels called successfully`));

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error toggling channel autopublisher:`), error);
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} Failed to toggle channel autopublisher. Please try again.`,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} Failed to toggle channel autopublisher. Please try again.`,
                    ephemeral: true
                });
            }
        }
    },

    async showChangeChannel(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.CHANNELS} Change Autopublisher Channel`)
            .setDescription('Provide the new channel ID for auto publishing:')
            .addFields([
                {
                    name: 'Instructions',
                    value: '1. **Send the Channel ID** in this chat\n2. You can get the Channel ID by right-clicking on a channel and selecting "Copy Channel ID"\n3. The channel must be an **Announcement Channel** (News Channel)\n4. This will replace your current channel configuration\n\n*You have 60 seconds to respond.*',
                    inline: false
                },
                {
                    name: 'Requirements',
                    value: '• Channel must be an Announcement/News channel\n• Bot must have "Manage Messages" permission in the channel',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('autopublisher_back')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row]
        });

        this.updateSession(interaction.user.id, { 
            step: 'changing_channel',
            pendingChannelInput: {
                channelId: interaction.channel.id,
                userId: interaction.user.id
            }
        });

        // Create a collector to wait for the user's channel input
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 60000, // 1 minute timeout
            max: 1
        });

        collector.on('collect', async (message) => {
            try {
                await this.processChangeChannelInput(message, interaction);
            } catch (error) {
                console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing channel change input:`), error);
                await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
            }
        });

        collector.on('end', (collected) => {
            if (collected.size === 0) {
                interaction.followUp({
                    content: `${AUTOPUBLISHER_EMOJIS.TIMEOUT_WARNING} Channel input timed out. Please try again.`,
                    ephemeral: true
                }).catch(console.error);
            }
        });
    },

    async showChangeSpecificChannel(interaction, targetChannelId) {
        // Get current configuration to find the channel name
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const autopublisherConfig = currentConfig?.autopublisher || { channels: {} };
        const targetChannel = interaction.guild.channels.cache.get(targetChannelId);
        const channelName = targetChannel ? targetChannel.name : 'Unknown Channel';

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.CHANNELS} Change Autopublisher Channel`)
            .setDescription(`Provide the new channel ID to replace **${channelName}**:`)
            .addFields([
                {
                    name: 'Current Channel',
                    value: targetChannel ? `<#${targetChannelId}> (${channelName})` : `~~Unknown Channel (${targetChannelId})~~`,
                    inline: false
                },
                {
                    name: 'Instructions',
                    value: '1. **Send the Channel ID** in this chat\n2. You can get the Channel ID by right-clicking on a channel and selecting "Copy Channel ID"\n3. The channel must be an **Announcement Channel** (News Channel)\n4. This will replace the selected channel configuration\n\n*You have 60 seconds to respond.*',
                    inline: false
                },
                {
                    name: 'Requirements',
                    value: '• Channel must be an Announcement/News channel\n• Bot must have "Manage Messages" permission in the channel',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('autopublisher_back')
                    .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row]
        });

        this.updateSession(interaction.user.id, { 
            step: 'changing_specific_channel',
            targetChannelId: targetChannelId,
            pendingChannelInput: {
                channelId: interaction.channel.id,
                userId: interaction.user.id
            }
        });

        // Create a collector to wait for the user's channel input
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 60000, // 1 minute timeout
            max: 1
        });

        collector.on('collect', async (message) => {
            try {
                await this.processChangeSpecificChannelInput(message, interaction, targetChannelId);
            } catch (error) {
                console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing specific channel change input:`), error);
                await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
            }
        });

        collector.on('end', (collected) => {
            if (collected.size === 0) {
                interaction.followUp({
                    content: `${AUTOPUBLISHER_EMOJIS.TIMEOUT_WARNING} Channel input timed out. Please try again.`,
                    ephemeral: true
                }).catch(console.error);
            }
        });
    },

    async processChangeChannelInput(message, originalInteraction) {
        try {
            const channelInput = message.content.trim();
            let channelId = null;

            // Parse channel mention or ID
            const mentionMatch = channelInput.match(/^<#(\d+)>$/);
            if (mentionMatch) {
                channelId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(channelInput)) {
                channelId = channelInput;
            }
            else {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a channel mention (#channel-name) or a numeric channel ID.`);
            }

            // Validate the channel exists and is an announcement channel
            const channel = message.guild.channels.cache.get(channelId);
            if (!channel) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.CHANNEL_NOT_FOUND} Channel not found. Please make sure the channel exists in this server and try again.`);
            }

            if (channel.type !== ChannelType.GuildAnnouncement) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_CHANNEL_TYPE} Invalid channel type. Please provide an **Announcement Channel** (News Channel) for auto publishing.`);
            }

            // Save the channel change
            await this.saveChannelChange(message, channelId, channel.name, originalInteraction);

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing channel change input:`), error);
            await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
        }
    },

    async processChangeSpecificChannelInput(message, originalInteraction, targetChannelId) {
        try {
            const channelInput = message.content.trim();
            let channelId = null;

            // Parse channel mention or ID
            const mentionMatch = channelInput.match(/^<#(\d+)>$/);
            if (mentionMatch) {
                channelId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(channelInput)) {
                channelId = channelInput;
            }
            else {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a channel mention (#channel-name) or a numeric channel ID.`);
            }

            // Validate the channel exists and is an announcement channel
            const channel = message.guild.channels.cache.get(channelId);
            if (!channel) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.CHANNEL_NOT_FOUND} Channel not found. Please make sure the channel exists in this server and try again.`);
            }

            if (channel.type !== ChannelType.GuildAnnouncement) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.INVALID_CHANNEL_TYPE} Invalid channel type. Please provide an **Announcement Channel** (News Channel) for auto publishing.`);
            }

            // Check if channel is already configured (but allow if it's the same channel being replaced)
            const currentConfig = await configService.getServerConfig(message.guild.id);
            const autopublisherConfig = currentConfig?.autopublisher || { channels: {} };
            
            if (autopublisherConfig.channels && autopublisherConfig.channels[channelId] && channelId !== targetChannelId) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.WARNING} This channel is already configured for auto publishing.`);
            }

            // Save the specific channel change
            await this.saveSpecificChannelChange(message, channelId, channel.name, targetChannelId, originalInteraction);

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error processing specific channel change input:`), error);
            await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
        }
    },

    async saveChannelChange(message, newChannelId, channelName, originalInteraction) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }
            
            // Get current configuration
            let config = await configService.getServerConfig(message.guild.id);
            if (!config?.autopublisher?.channels) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} No autopublisher configuration found.`);
            }

            // Get the current channel ID (should be only one since we're in change mode)
            const currentChannels = Object.keys(config.autopublisher.channels);
            if (currentChannels.length !== 1) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} Invalid configuration state. Please restart the setup.`);
            }

            const oldChannelId = currentChannels[0];
            const oldConfig = config.autopublisher.channels[oldChannelId];

            // Create a deep copy to ensure proper saving (fixes MongoDB document modification issue)
            const newConfig = JSON.parse(JSON.stringify(config));
            
            // Remove old channel and add new one with same settings
            delete newConfig.autopublisher.channels[oldChannelId];
            newConfig.autopublisher.channels[newChannelId] = {
                enabled: oldConfig.enabled,
                name: channelName,
                addedAt: new Date()
            };

            // Save to database
            await configService.saveServerConfig(message.guild.id, newConfig);
            
            console.log(chalk.green(`✅ Channel changed from ${oldChannelId} to ${newChannelId} for user ${message.author.id}`));
            
            // Show success and return to manage interface
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${AUTOPUBLISHER_EMOJIS.SUCCESS} Channel Changed Successfully`)
                .setDescription(`Successfully changed autopublisher channel to <#${newChannelId}>!`)
                .addFields([
                    {
                        name: 'New Channel',
                        value: `<#${newChannelId}> ${channelName ? `(${channelName})` : ''}`,
                        inline: false
                    },
                    {
                        name: 'Status',
                        value: `${oldConfig.enabled ? `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled**` : `${AUTOPUBLISHER_EMOJIS.DISABLED} **Disabled**`} - Settings preserved from previous channel`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const successRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_back')
                        .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back to Management`)
                        .setStyle(ButtonStyle.Primary)
                );

            // Update the original interaction
            await originalInteraction.editReply({ 
                embeds: [successEmbed], 
                components: [successRow] 
            });
            
            console.log(chalk.green(`✅ Interface updated successfully with channel change`));
            
        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error saving channel change:`), error);
            try {
                const errorMessage = await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while saving the channel change. Please try again.`);
                
                // Auto-delete the error message after 10 seconds
                setTimeout(async () => {
                    try {
                        await errorMessage.delete();
                    } catch (deleteError) {
                        console.log(chalk.yellow(`⚠️ Could not delete error message: ${deleteError.message}`));
                    }
                }, 10000);
            } catch (replyError) {
                console.log(chalk.red(`❌ Could not send error reply: ${replyError.message}`));
            }
        }
    },

    async saveSpecificChannelChange(message, newChannelId, channelName, targetChannelId, originalInteraction) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }
            
            // Get current configuration
            let config = await configService.getServerConfig(message.guild.id);
            if (!config?.autopublisher?.channels) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} No autopublisher configuration found.`);
            }

            // Check if target channel exists in configuration
            if (!config.autopublisher.channels[targetChannelId]) {
                return await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} Target channel configuration not found.`);
            }

            const oldConfig = config.autopublisher.channels[targetChannelId];

            // Create a deep copy to ensure proper saving (fixes MongoDB document modification issue)
            const newConfig = JSON.parse(JSON.stringify(config));
            
            // Remove old channel and add new one with same settings
            delete newConfig.autopublisher.channels[targetChannelId];
            newConfig.autopublisher.channels[newChannelId] = {
                enabled: oldConfig.enabled,
                name: channelName,
                addedAt: new Date()
            };

            // Save to database
            await configService.saveServerConfig(message.guild.id, newConfig);
            
            console.log(chalk.green(`✅ Channel changed from ${targetChannelId} to ${newChannelId} for user ${message.author.id}`));
            
            // Show success and return to manage interface
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${AUTOPUBLISHER_EMOJIS.SUCCESS} Channel Changed Successfully`)
                .setDescription(`Successfully changed autopublisher channel to <#${newChannelId}>!`)
                .addFields([
                    {
                        name: 'New Channel',
                        value: `<#${newChannelId}> ${channelName ? `(${channelName})` : ''}`,
                        inline: false
                    },
                    {
                        name: 'Status',
                        value: `${oldConfig.enabled ? `${AUTOPUBLISHER_EMOJIS.ENABLED} **Enabled**` : `${AUTOPUBLISHER_EMOJIS.DISABLED} **Disabled**`} - Settings preserved from previous channel`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const successRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('autopublisher_back')
                        .setLabel(`${AUTOPUBLISHER_EMOJIS.BACK} Back to Management`)
                        .setStyle(ButtonStyle.Primary)
                );

            // Update the original interaction
            await originalInteraction.editReply({ 
                embeds: [successEmbed], 
                components: [successRow] 
            });
            
            console.log(chalk.green(`✅ Interface updated successfully with specific channel change`));
            
        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error saving specific channel change:`), error);
            try {
                const errorMessage = await message.reply(`${AUTOPUBLISHER_EMOJIS.ERROR} An error occurred while saving the channel change. Please try again.`);
                
                // Auto-delete the error message after 10 seconds
                setTimeout(async () => {
                    try {
                        await errorMessage.delete();
                    } catch (deleteError) {
                        console.log(chalk.yellow(`⚠️ Could not delete error message: ${deleteError.message}`));
                    }
                }, 10000);
            } catch (replyError) {
                console.log(chalk.red(`❌ Could not send error reply: ${replyError.message}`));
            }
        }
    },

    async handleCancel(interaction) {
        this.clearSession(interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${AUTOPUBLISHER_EMOJIS.CANCEL} Autopublisher Setup Cancelled`)
            .setDescription('Autopublisher setup has been cancelled. You can start a new setup anytime using `/autopublisher`.')
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [] });
    },

    async handleBackNavigation(interaction) {
        const session = this.getSession(interaction.user.id);
        if (!session) {
            return await interaction.update({
                content: `${AUTOPUBLISHER_EMOJIS.ERROR} No active setup session found. Please start a new setup.`,
                embeds: [],
                components: []
            });
        }

        // Navigate back based on current step
        switch (session.step) {
            case 'adding_channel':
            case 'changing_channel':
            case 'changing_specific_channel':
            case 'managing_channels':
                await this.showAutopublisherMain(interaction);
                break;
            default:
                await this.showAutopublisherMain(interaction);
                break;
        }
    },

    // Handle button interactions
    async handleButtonInteraction(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: `${AUTOPUBLISHER_EMOJIS.ERROR} No active autopublisher session found. Please start a new setup.`,
                    ephemeral: true
                });
            }

            const customId = interaction.customId;

            if (customId === 'autopublisher_add_channel') {
                await this.showAddChannel(interaction);
            } else if (customId === 'autopublisher_manage_channels') {
                await this.showManageChannels(interaction);
            } else if (customId === 'autopublisher_toggle_global') {
                await this.toggleGlobalAutopublisher(interaction);
            } else if (customId === 'autopublisher_cancel') {
                await this.handleCancel(interaction);
            } else if (customId === 'autopublisher_back') {
                await this.handleBackNavigation(interaction);
            } else if (customId === 'autopublisher_change_channel') {
                await this.showChangeChannel(interaction);
            } else if (customId.startsWith('autopublisher_change_channel_')) {
                // Handle change channel for specific channel in multiple channel setup
                const channelId = customId.replace('autopublisher_change_channel_', '');
                await this.showChangeSpecificChannel(interaction, channelId);
            } else if (customId.startsWith('autopublisher_toggle_')) {
                const channelId = customId.replace('autopublisher_toggle_', '');
                // console.log(chalk.blue(`🔍 DEBUG: Button clicked - customId: "${customId}"`));
                // console.log(chalk.blue(`🔍 DEBUG: Extracted channelId: "${channelId}"`));
                await this.toggleChannelAutopublisher(interaction, channelId);
            }

        } catch (error) {
            console.error(chalk.red(`${AUTOPUBLISHER_EMOJIS.ERROR} Error handling button interaction:`), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    getDetailedErrorMessage(error) {
        if (error.code === 50013) {
            return `${AUTOPUBLISHER_EMOJIS.ERROR} I don\'t have the required permissions. Please ensure I have "Manage Messages" permission.`;
        } else if (error.code === 50001) {
            return `${AUTOPUBLISHER_EMOJIS.ERROR} Missing access to the specified channel. Please check permissions.`;
        } else if (error.code === 10003) {
            return `${AUTOPUBLISHER_EMOJIS.ERROR} The specified channel was not found. It may have been deleted.`;
        } else if (error.message?.includes('timeout')) {
            return `${AUTOPUBLISHER_EMOJIS.ERROR} The operation timed out. Please try again.`;
        } else {
            return `${AUTOPUBLISHER_EMOJIS.ERROR} An unexpected error occurred. Please try again or contact support if the issue persists.`;
        }
    },

    createSession(userId, sessionData) {
        autopublisherSessions.set(userId, sessionData);
        
        // Auto-cleanup session after timeout
        setTimeout(() => {
            autopublisherSessions.delete(userId);
        }, SESSION_TIMEOUT);
    },

    updateSession(userId, updates) {
        const session = autopublisherSessions.get(userId);
        if (session) {
            Object.assign(session, updates);
        } else {
            this.createSession(userId, updates);
        }
    },

    getSession(userId) {
        return autopublisherSessions.get(userId);
    },

    clearSession(userId) {
        autopublisherSessions.delete(userId);
    }
};


setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of autopublisherSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            autopublisherSessions.delete(userId);
        }
    }
}, 60000); 