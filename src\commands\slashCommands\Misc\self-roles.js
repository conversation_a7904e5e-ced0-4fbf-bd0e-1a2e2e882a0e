const { SlashCommandBuilder } = require('@discordjs/builders');
const { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, PermissionFlagsBits } = require('discord.js');
const Canvas = require('canvas');
const path = require('path');
module.exports = {
  data: new SlashCommandBuilder()
    .setName('self-roles')
    .setDescription('Displays the self roles.'),
  
    name: 'self-roles',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: 'self-roles',
    description: 'Displays the self roles.',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,

    async execute(interaction) {

        await interaction.deferReply();
    
        try {
          // Create the ping roles image buffer
          const pingRolesImageBuffer = await createPingRolesImage();
          
          // Create the ping roles embed
          const pingRolesEmbed = new EmbedBuilder()
            .setColor('#164ee9')
            .setTitle('📢 DUNGEON PING ROLES')
            .setImage('attachment://ping-roles.png');
          
          // Create the select menu for ping roles
          const pingRolesMenu = new StringSelectMenuBuilder()
            .setCustomId('ping-roles-menu')
            .setPlaceholder('Select ping roles to add/remove')
            .setMinValues(0)
            .setMaxValues(12) // Maximum number of roles that can be selected
            .addOptions([
              new StringSelectMenuOptionBuilder()
                .setLabel('Dungeons Ping')
                .setDescription('General ping for dungeon-related announcements')
                .setValue('dungeons_ping')
                .setEmoji('🌀'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Double Dungeon Ping')
                .setDescription('For double dungeon rewards and events')
                .setValue('double_dungeon_ping')
                .setEmoji('✨'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Red Gate Ping')
                .setDescription('For Red Gate special alert missions')
                .setValue('red_gate_ping')
                .setEmoji('🚪'),
              new StringSelectMenuOptionBuilder()
                .setLabel('SS Dungeon Ping')
                .setDescription('For high-level SS Dungeon raids')
                .setValue('ss_dungeon_ping')
                .setEmoji('💎'),
              new StringSelectMenuOptionBuilder()
                .setLabel('S Dungeon Ping')
                .setDescription('For S-tier dungeon openings')
                .setValue('s_dungeon_ping')
                .setEmoji('🔥'),
              new StringSelectMenuOptionBuilder()
                .setLabel('A Dungeon Ping')
                .setDescription('For A-tier dungeon hunts')
                .setValue('a_dungeon_ping')
                .setEmoji('💠'),
              new StringSelectMenuOptionBuilder()
                .setLabel('B Dungeon Ping')
                .setDescription('For B-tier dungeon explorations')
                .setValue('b_dungeon_ping')
                .setEmoji('🟣'),
              new StringSelectMenuOptionBuilder()
                .setLabel('C Dungeon Ping')
                .setDescription('For C-tier dungeon missions')
                .setValue('c_dungeon_ping')
                .setEmoji('🔵'),
              new StringSelectMenuOptionBuilder()
                .setLabel('D Dungeon Ping')
                .setDescription('For D-tier dungeon groups')
                .setValue('d_dungeon_ping')
                .setEmoji('🟢'),
              new StringSelectMenuOptionBuilder()
                .setLabel('E Dungeon Ping')
                .setDescription('For E-tier dungeon starters')
                .setValue('e_dungeon_ping')
                .setEmoji('🟤'),
              new StringSelectMenuOptionBuilder()
                .setLabel('N Rank Ping')
                .setDescription('For N-rank dungeon notifications')
                .setValue('n_rank_ping')
                .setEmoji('🔶'),
              new StringSelectMenuOptionBuilder()
                .setLabel('G Rank Ping')
                .setDescription('For G-rank dungeon notifications')
                .setValue('g_rank_ping')
                .setEmoji('🟨')
            ]);
          
          // Add the ping roles select menu to an action row
          const pingRolesRow = new ActionRowBuilder().addComponents(pingRolesMenu);
          
          // Create the infernal roles image buffer
          const infernalRolesImageBuffer = await createInfernalRolesImage();
          
          // Create the infernal roles embed
          const infernalRolesEmbed = new EmbedBuilder()
            .setColor('#164ee9')
            .setTitle('🔥 INFERNAL PING ROLES')
            .setImage('attachment://infernal-roles.png');
          
          // Create the select menu for infernal roles
          const infernalRolesMenu = new StringSelectMenuBuilder()
            .setCustomId('infernal-roles-menu')
            .setPlaceholder('Select infernal roles to add/remove')
            .setMinValues(0)
            .setMaxValues(3) // Maximum number of roles that can be selected
            .addOptions([
              new StringSelectMenuOptionBuilder()
                .setLabel('Infernal Ping')
                .setDescription('For infernal content and announcements')
                .setValue('infernal_ping')
                .setEmoji('🔥'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Monarch Ping')
                .setDescription('For monarch-level content and events')
                .setValue('monarch_ping')
                .setEmoji('👑'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Dae In Ping')
                .setDescription('For Dae In related activities')
                .setValue('dae_in_ping')
                .setEmoji('⚔️')
            ]);
          
          // Add the infernal roles select menu to an action row
          const infernalRolesRow = new ActionRowBuilder().addComponents(infernalRolesMenu);
          
          // Create the World 1 roles image buffer
          const world1RolesImageBuffer = await createWorld1RolesImage();

          // Create the World 1 roles embed
          const world1RolesEmbed = new EmbedBuilder()
            .setColor('#164ee9')
            .setTitle('🏝️ WORLD 1 PING ROLES')
            .setImage('attachment://world1-roles.png');

          // Create the select menu for World 1 roles
          const world1RolesMenu = new StringSelectMenuBuilder()
            .setCustomId('world1-roles-menu')
            .setPlaceholder('Select World 1 roles to add/remove')
            .setMinValues(0)
            .setMaxValues(8) // Maximum number of roles that can be selected
            .addOptions([
              new StringSelectMenuOptionBuilder()
                .setLabel('World 1 Ping')
                .setDescription('For World 1 dungeon announcements')
                .setValue('world_1_ping')
                .setEmoji('🌍'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Leveling City')
                .setDescription('Get the Leveling City role')
                .setValue('leveling_city')
                .setEmoji('🏙️'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Grass Village')
                .setDescription('Get the Grass Village role')
                .setValue('grass_village')
                .setEmoji('🌿'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Brum Island')
                .setDescription('Get the Brum Island role')
                .setValue('brum_island')
                .setEmoji('🏝️'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Faceheal Town')
                .setDescription('Get the Faceheal Town role')
                .setValue('faceheal_town')
                .setEmoji('🏥'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Lucky Kingdom')
                .setDescription('Get the Lucky Kingdom role')
                .setValue('lucky_kingdom')
                .setEmoji('🍀'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Nipon City')
                .setDescription('Get the Nipon City role')
                .setValue('nipon_city')
                .setEmoji('🗼'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Mori Town')
                .setDescription('Get the Mori Town role')
                .setValue('mori_town')
                .setEmoji('🌳')
            ]);

          // Add the World 1 roles select menu to an action row
          const world1RolesRow = new ActionRowBuilder().addComponents(world1RolesMenu);

          // Create the World 2 roles image buffer
          const world2RolesImageBuffer = await createWorld2RolesImage();

          // Create the World 2 roles embed
          const world2RolesEmbed = new EmbedBuilder()
            .setColor('#164ee9')
            .setTitle('🌎 WORLD 2 PING ROLES')
            .setImage('attachment://world2-roles.png');

          // Create the select menu for World 2 roles
          const world2RolesMenu = new StringSelectMenuBuilder()
            .setCustomId('world2-roles-menu')
            .setPlaceholder('Select World 2 roles to add/remove')
            .setMinValues(0)
            .setMaxValues(7) // Maximum number of roles that can be selected
            .addOptions([
              new StringSelectMenuOptionBuilder()
                .setLabel('World 2 Ping')
                .setDescription('For World 2 dungeon announcements')
                .setValue('world_2_ping')
                .setEmoji('🌎'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Dragon City')
                .setDescription('Get the Dragon City role')
                .setValue('dragon_city')
                .setEmoji('🐉'),
              new StringSelectMenuOptionBuilder()
                .setLabel('XZ City')
                .setDescription('Get the XZ City role')
                .setValue('xz_city')
                .setEmoji('🏛️'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Kindama City')
                .setDescription('Get the Kindama City role')
                .setValue('kindama_city')
                .setEmoji('✨'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Hunters City')
                .setDescription('Get the Hunters City role')
                .setValue('hunter_city')
                .setEmoji('🏹'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Nen City')
                .setDescription('Get the Nen City role')
                .setValue('nen_city')
                .setEmoji('🏯'),
              new StringSelectMenuOptionBuilder()
                .setLabel('Cursed High')
                .setDescription('Get the Cursed High role')
                .setValue('cursed_high')
                .setEmoji('🏔️'),
            ]);

          // Add the World 2 roles select menu to an action row
          const world2RolesRow = new ActionRowBuilder().addComponents(world2RolesMenu);
          
          // Send the embed with the image and menu
          await interaction.editReply({
            content: 'Self-roles menus have been set up successfully!',
            ephemeral: true
          });

          await interaction.channel.send("# <a:Diamond:1367792819057594390> SELF ROLES")
          
          // Send the ping roles embed with image and menu
          await interaction.channel.send({
            embeds: [pingRolesEmbed],
            files: [{ attachment: pingRolesImageBuffer, name: 'ping-roles.png' }],
            components: [pingRolesRow]
          });

          await interaction.channel.send({
            embeds: [world1RolesEmbed],
            files: [{ attachment: world1RolesImageBuffer, name: 'world1-roles.png' }],
            components: [world1RolesRow]
          });

          await interaction.channel.send({
            embeds: [world2RolesEmbed],
            files: [{ attachment: world2RolesImageBuffer, name: 'world2-roles.png' }],
            components: [world2RolesRow]
          });

          await interaction.channel.send({
            embeds: [infernalRolesEmbed],
            files: [{ attachment: infernalRolesImageBuffer, name: 'infernal-roles.png' }],
            components: [infernalRolesRow]
          });
        } catch (error) {
          console.error('Error setting up self roles:', error);
          await interaction.editReply('There was an error setting up the self roles menus.');
        }
      }
    };
    
    // Function to create the ping roles image
    async function createPingRolesImage() {
      const canvas = Canvas.createCanvas(850, 750);
      const ctx = canvas.getContext('2d');

      // Background
      ctx.fillStyle = '#18191c';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add border
      ctx.strokeStyle = '#164ee9';
      ctx.lineWidth = 5;
      ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);

      // Ping roles
      const roles = [
        { emoji: '🌀', role: 'Dungeons Ping', desc: 'General ping for dungeon-related announcements.' },
        { emoji: '✨', role: 'Double Dungeon Ping', desc: 'For double dungeon rewards and events.' },
        { emoji: '🚪', role: 'Red Gate Ping', desc: 'For Red Gate special alert missions.' },
        { emoji: '💎', role: 'SS Dungeon Ping', desc: 'For high-level SS Dungeon raids.' },
        { emoji: '🔥', role: 'S Dungeon Ping', desc: 'For S-tier dungeon openings.' },
        { emoji: '💠', role: 'A Dungeon Ping', desc: 'For A-tier dungeon hunts.' },
        { emoji: '🟣', role: 'B Dungeon Ping', desc: 'For B-tier dungeon explorations.' },
        { emoji: '🔵', role: 'C Dungeon Ping', desc: 'For C-tier dungeon missions.' },
        { emoji: '🟢', role: 'D Dungeon Ping', desc: 'For D-tier dungeon groups.' },
        { emoji: '🟤', role: 'E Dungeon Ping', desc: 'For E-tier dungeon starters.' },
        { emoji: '🔶', role: 'N Rank Ping', desc: 'For N-rank dungeon notifications.' },
        { emoji: '🟨', role: 'G Rank Ping', desc: 'For G-rank dungeon notifications.' }
      ];

      // Draw roles
      let y = 50;

      for (const role of roles) {
        // Role name with emoji
        ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'left';
        ctx.fillText(`${role.emoji} ${role.role}`, 50, y);

        y += 25;

        // Description
        ctx.font = '18px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#b9bbbe';
        ctx.fillText(role.desc, 70, y);

        y += 32;
      }

      ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
      ctx.fillStyle = 'rgba(22, 78, 233, 1)'; // Semi-transparent orange to match the border
      ctx.textAlign = 'center';
      ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);

      return canvas.toBuffer();
    }

    // Function to create the infernal roles image
    async function createInfernalRolesImage() {
      const canvas = Canvas.createCanvas(900, 250);
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = '#18191c';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Add border
      ctx.strokeStyle = '#164ee9'; 
      ctx.lineWidth = 5;
      ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);
      
      // Infernal roles
      const roles = [
        { emoji: '🔥', role: 'Infernal Ping', desc: 'For Infernal Castle Spawn Ping.' },
        { emoji: '👑', role: 'Monarch Ping', desc: 'For Monarch spotted in Infernal.' },
        { emoji: '⚔️', role: 'Dae In Ping', desc: 'For Dae In spotted in Infernal.' }
      ];
      
      // Draw roles
      let y = 60;
      
      for (const role of roles) {
        // Role name with emoji
        ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'left';
        ctx.fillText(`${role.emoji} ${role.role}`, 50, y);
        
        y += 30;
        
        // Description
        ctx.font = '18px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#b9bbbe';
        ctx.fillText(role.desc, 70, y);
        
        y += 32;
      }
    
      ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
      ctx.fillStyle = 'rgba(22, 78, 233, 1)'; 
      ctx.textAlign = 'center';
      ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
      
      return canvas.toBuffer();
    }

    // Function to create the World 1 roles image
    async function createWorld1RolesImage() {
      const canvas = Canvas.createCanvas(900, 510);
      const ctx = canvas.getContext('2d');

      // Background
      ctx.fillStyle = '#18191c';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add border
      ctx.strokeStyle = '#164ee9';
      ctx.lineWidth = 5;
      ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);

      // World 1 roles
      const roles = [
        { emoji: '🌍', role: 'World 1 Ping', desc: 'For World 1 dungeon announcements.' },
        { emoji: '🏙️', role: 'Leveling City', desc: 'Role for Leveling City Dungeons ping.' },
        { emoji: '🌿', role: 'Grass Village', desc: 'Role for Grass Dungeons ping.' },
        { emoji: '🏝️', role: 'Brum Island', desc: 'Role for Brum Island Dungeons ping.' },
        { emoji: '🏥', role: 'Faceheal Town', desc: 'Role for Faceheal Town Dungeons ping.' },
        { emoji: '🍀', role: 'Lucky Kingdom', desc: 'Role for Lucky Kingdom Dungeons ping.' },
        { emoji: '🗼', role: 'Nipon City', desc: 'Role for Nipon City Dungeons ping.' },
        { emoji: '🌳', role: 'Mori Town', desc: 'Role for Mori Town Dungeons ping.' }
      ];

      // Draw roles
      let y = 50;

      for (const role of roles) {
        // Role name with emoji
        ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'left';
        ctx.fillText(`${role.emoji} ${role.role}`, 50, y);

        y += 25;

        // Description
        ctx.font = '18px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#b9bbbe';
        ctx.fillText(role.desc, 70, y);

        y += 32;
      }

      ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
      ctx.fillStyle = 'rgba(22, 78, 233, 1)';
      ctx.textAlign = 'center';
      ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);

      return canvas.toBuffer();
    }

    // Function to create the World 2 roles image
    async function createWorld2RolesImage() {
      const canvas = Canvas.createCanvas(900, 450);
      const ctx = canvas.getContext('2d');

      // Background
      ctx.fillStyle = '#18191c';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add border
      ctx.strokeStyle = '#164ee9';
      ctx.lineWidth = 5;
      ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);

      // World 2 roles
      const roles = [
        { emoji: '🌎', role: 'World 2 Ping', desc: 'For World 2 dungeon announcements.' },
        { emoji: '🐉', role: 'Dragon City', desc: 'Role for Dragon City Dungeons ping.' },
        { emoji: '🏛️', role: 'XZ City', desc: 'Role for XZ City Dungeons ping.' },
        { emoji: '✨', role: 'Kindama City', desc: 'Role for Kindama City Dungeons ping.' },
        { emoji: '🏹', role: 'Hunters City', desc: 'Role for Hunters City Dungeons ping.' },
        { emoji: '🏯', role: 'Nen City', desc: 'Role for Nen City Dungeons ping.' },
        { emoji: '🏔️', role: 'Cursed High', desc: 'Role for Cursed High Dungeons ping.' }
      ];

      // Draw roles
      let y = 50;

      for (const role of roles) {
        // Role name with emoji
        ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'left';
        ctx.fillText(`${role.emoji} ${role.role}`, 50, y);

        y += 25;

        // Description
        ctx.font = '18px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#b9bbbe';
        ctx.fillText(role.desc, 70, y);

        y += 32;
      }

      ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
      ctx.fillStyle = 'rgba(22, 78, 233, 1)';
      ctx.textAlign = 'center';
      ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);

      return canvas.toBuffer();
    }