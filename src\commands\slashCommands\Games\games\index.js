const fs = require('fs');
const path = require('path');
const { SlashCommandBuilder } = require('discord.js');

const command = {
    data: new SlashCommandBuilder()
        .setName('games')
        .setDescription('Play various mini-games with other users'),
    
    name: 'games',
    category: 'Games',
    aliases: [],
    cooldown: 5,
    usage: 'games <subcommand>',
    description: 'Play various games with other users',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 1,
    nsfw: false,
    BotOwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both',

    // Map subcommand name → module
    subcommands: new Map(),

    // Build subcommands and populate subcommands map
    registerSubcommands() {
        const folder = __dirname;
        const files = fs.readdirSync(folder)
            .filter(f => f !== 'index.js' && f.endsWith('.js'));
        
        for (const file of files) {
            try {
                const mod = require(path.join(folder, file));
                // Each module must export .data as a SlashCommandSubcommandBuilder
                if (mod.data && mod.data.name) {
                    this.data.addSubcommand(mod.data);
                    this.subcommands.set(mod.data.name, mod);
                    console.log(`✅ Loaded game subcommand: ${mod.data.name}`);
                }
            } catch (error) {
                console.error(`❌ Error loading game module ${file}:`, error);
            }
        }
    },

    async execute(interaction) {
        const sub = interaction.options.getSubcommand();
        const mod = this.subcommands.get(sub);
        if (!mod) {
            return interaction.reply({ 
                content: `❌ Unknown game: ${sub}. Available games: ${Array.from(this.subcommands.keys()).join(', ')}`, 
                ephemeral: true 
            });
        }
        return mod.execute(interaction);
    },

    // Dispatched from global interactionCreate handler
    async handleButtonInteraction(interaction) {
        const customId = interaction.customId;
        // Prefix is the substring up to first underscore
        const prefix = customId.split('_')[0];
        
        // Special handling for tic-tac-toe (uses 'ttt' prefix)
        const gamePrefix = prefix === 'ttt' ? 'tictactoe' : prefix;
        
        const mod = this.subcommands.get(gamePrefix);
        if (!mod || typeof mod.handleButtonInteraction !== 'function') {
            console.log(`No button handler found for prefix: ${prefix} (mapped to: ${gamePrefix})`);
            return interaction.reply({ 
                content: '❌ No game handler found for this button interaction.', 
                ephemeral: true 
            });
        }
        return mod.handleButtonInteraction(interaction);
    }
};

// Register subcommands before export
command.registerSubcommands();

module.exports = command;
