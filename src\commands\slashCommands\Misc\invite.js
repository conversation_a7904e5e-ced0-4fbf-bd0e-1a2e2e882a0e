const { <PERSON>lash<PERSON>ommandB<PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder } = require('discord.js');
const { createCanvas, loadImage } = require('canvas');
const config = require('../../../../config/config.js');
const path = require('path');

const cmdname = path.parse(__filename).name; // file name
const folderName = path.basename(__dirname); // folder name

module.exports = {
    data: new SlashCommandBuilder()
        .setName('invite')
        .setDescription('Get invite links for the bot and support server'),
    name: cmdname,
    category: folderName,
    aliases: ['inv'],
    cooldown: 10,
    usage: 'invite',
    description: 'Get invite links for the bot and support server',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both', // This command can be used both as a user app and in guilds

    async execute(interaction) {
        // Create canvas
        const canvas = createCanvas(800, 200);
        const ctx = canvas.getContext('2d');

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 800, 200);
        gradient.addColorStop(0, '#2563eb');
        gradient.addColorStop(0.3, '#1e40af');
        gradient.addColorStop(0.7, '#3b82f6');
        gradient.addColorStop(1, '#60a5fa');

        // Fill background
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 800, 200);

        // Add subtle overlay pattern
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        for (let i = 0; i < 800; i += 40) {
            for (let j = 0; j < 200; j += 40) {
                ctx.fillRect(i, j, 20, 20);
            }
        }

        // Add main title
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Invite RankBreaker', 400, 80);

        // Add subtitle
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = '24px Arial';
        ctx.fillText('Level up your Discord server!', 400, 120);

        // Add decorative elements
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.beginPath();
        ctx.arc(100, 100, 30, 0, Math.PI * 2);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(700, 100, 25, 0, Math.PI * 2);
        ctx.fill();

        // Add bot icon if available
        try {
            const avatar = await loadImage(interaction.client.user.displayAvatarURL({ extension: 'png', size: 128 }));
            ctx.save();
            ctx.beginPath();
            ctx.arc(400, 150, 25, 0, Math.PI * 2);
            ctx.clip();
            ctx.drawImage(avatar, 375, 125, 50, 50);
            ctx.restore();
        } catch (error) {
            // If avatar fails to load, add a simple circle
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(400, 150, 25, 0, Math.PI * 2);
            ctx.fill();
        }

        // Convert canvas to buffer
        const buffer = canvas.toBuffer('image/png');
        const attachment = new AttachmentBuilder(buffer, { name: 'invite-banner.png' });

        const embed = new EmbedBuilder()
            .setTitle('Invite RankBreaker!')
            // .setDescription('Click the buttons below to invite the bot to your server or join our support server!')
            .setColor(config.EmbedConfig.embedcolor)
            // .addFields(
            //     {
            //         name: '🔗 Bot Invite',
            //         value: 'Add RankBreaker to your server with all necessary permissions',
            //         inline: true
            //     },
            //     {
            //         name: '🏠 Support Server',
            //         value: 'Join our community for help, updates, and more!',
            //         inline: true
            //     }
            // )
            .setImage('attachment://invite-banner.png')
            .setFooter({ 
                text: 'Thanks for using RankBreaker!', 
                iconURL: interaction.client.user.displayAvatarURL({ dynamic: true }) 
            })
            .setTimestamp();

        const inviteButton = new ButtonBuilder()
            .setLabel('Invite Me!')
            .setStyle(ButtonStyle.Link)
            .setURL('https://discordapp.com/api/oauth2/authorize?client_id=1364252974393851977&permissions=543312834047&scope=bot+applications.commands');

        const supportButton = new ButtonBuilder()
            .setLabel('Join RankBreaker!')
            .setStyle(ButtonStyle.Link)
            .setURL('https://discord.gg/M6e3PGRtd3');

        const row = new ActionRowBuilder()
            .addComponents(inviteButton, supportButton);

        await interaction.reply({ 
            embeds: [embed], 
            components: [row],
            files: [attachment]
        });
    },
};