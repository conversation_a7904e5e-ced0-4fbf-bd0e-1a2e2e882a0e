const {
    <PERSON><PERSON><PERSON>ommandSubcommand<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');
const config = require('../../../../../config/config.js');
const events = require('events');

// Constants
const HEIGHT = 10;
const WIDTH = 15;

// Active games map
const activeSnakeGames = new Map();

// Snake Game Class
class SnakeGame extends events {
    constructor(options = {}) {
        super();
        if (!options.isSlashGame) options.isSlashGame = false;
        if (!options.message) throw new TypeError('NO_MESSAGE: No message option was provided.');
        if (typeof options.message !== 'object') throw new TypeError('INVALID_MESSAGE: message option must be an object.');
        if (typeof options.isSlashGame !== 'boolean') throw new TypeError('INVALID_COMMAND_TYPE: isSlashGame option must be a boolean.');

        if (!options.embed) options.embed = {};
        if (!options.embed.title) options.embed.title = 'Snake Game';
        if (!options.embed.color) options.embed.color = '#5865F2';
        if (!options.embed.overTitle) options.embed.overTitle = 'Game Over';

        if (!options.snake) options.snake = {};
        if (!options.snake.head) options.snake.head = '🟢';
        if (!options.snake.body) options.snake.body = '🟩';
        if (!options.snake.tail) options.snake.tail = '🟢';
        if (!options.snake.skull) options.snake.skull = '💀';

        if (!options.emojis) options.emojis = {};
        if (!options.emojis.board) options.emojis.board = '⬛';
        if (!options.emojis.food) options.emojis.food = '🍎';
        if (!options.emojis.up) options.emojis.up = '⬆️';
        if (!options.emojis.down) options.emojis.down = '⬇️';
        if (!options.emojis.left) options.emojis.left = '⬅️';
        if (!options.emojis.right) options.emojis.right = '➡️';

        if (!options.foods) options.foods = [];
        if (!options.stopButton) options.stopButton = 'Stop';
        if (!options.timeoutTime) options.timeoutTime = 60000;

        this.options = options;
        this.message = options.message;
        this.snake = [{ x: 5, y: 5 }];
        this.apple = { x: 1, y: 1 };
        this.snakeLength = 1;
        this.gameBoard = [];
        this.score = 0;
        for (let y = 0; y < HEIGHT; y++) {
            for (let x = 0; x < WIDTH; x++) {
                this.gameBoard[y * WIDTH + x] = options.emojis.board;
            }
        }
    }

    getBoardContent(isSkull = false) {
        const emojis = this.options.snake;
        let board = '';
        for (let y = 0; y < HEIGHT; y++) {
            for (let x = 0; x < WIDTH; x++) {
                if (x === this.apple.x && y === this.apple.y) { board += this.options.emojis.food; continue; }
                const snakePart = this.isSnake({ x, y });
                if (snakePart) {
                    const pos = this.snake.indexOf(snakePart);
                    if (pos === 0) {
                        board += (!isSkull || this.snakeLength >= HEIGHT * WIDTH) ? emojis.head : emojis.skull;
                    } else if (pos === this.snake.length - 1) {
                        board += emojis.tail;
                    } else {
                        board += emojis.body;
                    }
                } else {
                    board += this.gameBoard[y * WIDTH + x];
                }
            }
            board += '\n';
        }
        return board;
    }

    isSnake(pos) {
        return this.snake.find(s => s.x === pos.x && s.y === pos.y) ?? false;
    }

    updateFoodLoc() {
        let applePos;
        do {
            applePos = { x: Math.floor(Math.random() * WIDTH), y: Math.floor(Math.random() * HEIGHT) };
        } while (this.isSnake(applePos));
        this.apple = applePos;
    }

    async sendMessage(content) {
        return this.options.isSlashGame
            ? this.message.editReply(content)
            : this.message.channel.send(content);
    }

    async startGame() {
        if (this.options.isSlashGame || !this.message.author) {
            if (!this.message.deferred) await this.message.deferReply().catch(() => {});
            this.message.author = this.message.user;
            this.options.isSlashGame = true;
        }
        this.updateFoodLoc();
        const emojis = this.options.emojis;
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription(`**Score:** ${this.score}\n\n${this.getBoardContent()}`)
            .setFooter({ text: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        const up = new ButtonBuilder().setEmoji(emojis.up).setStyle(ButtonStyle.Primary).setCustomId('snake_up');
        const down = new ButtonBuilder().setEmoji(emojis.down).setStyle(ButtonStyle.Primary).setCustomId('snake_down');
        const left = new ButtonBuilder().setEmoji(emojis.left).setStyle(ButtonStyle.Primary).setCustomId('snake_left');
        const right = new ButtonBuilder().setEmoji(emojis.right).setStyle(ButtonStyle.Primary).setCustomId('snake_right');
        const stop = new ButtonBuilder().setLabel(this.options.stopButton).setStyle(ButtonStyle.Danger).setCustomId('snake_stop');
        const dis1 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis1').setDisabled(true);
        const dis2 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis2').setDisabled(true);
        const row1 = new ActionRowBuilder().addComponents(dis1, up, dis2, stop);
        const row2 = new ActionRowBuilder().addComponents(left, down, right);
        const msg = await this.sendMessage({ embeds: [embed], components: [row1, row2] });
        this.handleButtons(msg);
    }

    handleButtons(msg) {
        this.gameMessage = msg;
        this.gameTimeout = setTimeout(() => this.endGameWithInteraction({ update: (...a) => msg.edit(...a) }), this.options.timeoutTime);
    }

    async handleButtonClick(interaction) {
        try {
            if (interaction.user.id !== this.message.author.id) {
                if (this.options.playerOnlyMessage) {
                    await interaction.reply({ content: this.formatMessage(this.options, 'playerOnlyMessage'), ephemeral: true });
                }
                return;
            }
            if (this.gameTimeout) {
                clearTimeout(this.gameTimeout);
                this.gameTimeout = setTimeout(() => this.endGameWithInteraction(interaction), this.options.timeoutTime);
            }
            const head = this.snake[0];
            const next = { x: head.x, y: head.y };
            const dir = interaction.customId.split('_')[1];
            if (dir === 'left') next.x -= 1;
            else if (dir === 'right') next.x += 1;
            else if (dir === 'up') next.y -= 1;
            else if (dir === 'down') next.y += 1;

            if (next.x < 0 || next.x >= WIDTH || next.y < 0 || next.y >= HEIGHT || this.isSnake(next) || dir === 'stop') {
                return this.endGameWithInteraction(interaction);
            }
            this.snake.unshift(next);
            if (this.snake.length > this.snakeLength) this.snake.pop();
            await this.updateGameWithInteraction(interaction);
        } catch (err) {
            console.error(err);
        }
    }

    async updateGameWithInteraction(interaction) {
        if (this.apple.x === this.snake[0].x && this.apple.y === this.snake[0].y) {
            this.score += 1;
            this.snakeLength += 1;
            this.updateFoodLoc();
        }
        const emojis = this.options.emojis;
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.title)
            .setDescription(`**Score:** ${this.score}\n\n${this.getBoardContent()}`)
            .setFooter({ text: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        const up = new ButtonBuilder().setEmoji(emojis.up).setStyle(ButtonStyle.Primary).setCustomId('snake_up').setDisabled(false);
        const down = new ButtonBuilder().setEmoji(emojis.down).setStyle(ButtonStyle.Primary).setCustomId('snake_down').setDisabled(false);
        const left = new ButtonBuilder().setEmoji(emojis.left).setStyle(ButtonStyle.Primary).setCustomId('snake_left').setDisabled(false);
        const right = new ButtonBuilder().setEmoji(emojis.right).setStyle(ButtonStyle.Primary).setCustomId('snake_right').setDisabled(false);
        const stop = new ButtonBuilder().setLabel(this.options.stopButton).setStyle(ButtonStyle.Danger).setCustomId('snake_stop').setDisabled(false);
        const dis1 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis1').setDisabled(true);
        const dis2 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis2').setDisabled(true);
        const row1 = new ActionRowBuilder().addComponents(dis1, up, dis2, stop);
        const row2 = new ActionRowBuilder().addComponents(left, down, right);
        return interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async endGameWithInteraction(interaction) {
        if (this.gameTimeout) clearTimeout(this.gameTimeout);
        this.gameTimeout = null;
        const result = { player: this.message.author, score: this.score };
        this.emit('gameOver', { result: this.snakeLength >= HEIGHT * WIDTH ? 'win' : 'lose', ...result });
        const embed = new EmbedBuilder()
            .setColor(this.options.embed.color)
            .setTitle(this.options.embed.overTitle)
            .setDescription(`**Score:** ${this.score}\n\n${this.getBoardContent(true)}`)
            .setFooter({ text: this.message.author.tag, iconURL: this.message.author.displayAvatarURL({ dynamic: true }) });
        const emojis = this.options.emojis;
        const up = new ButtonBuilder().setEmoji(emojis.up).setStyle(ButtonStyle.Primary).setCustomId('snake_up').setDisabled(true);
        const down = new ButtonBuilder().setEmoji(emojis.down).setStyle(ButtonStyle.Primary).setCustomId('snake_down').setDisabled(true);
        const left = new ButtonBuilder().setEmoji(emojis.left).setStyle(ButtonStyle.Primary).setCustomId('snake_left').setDisabled(true);
        const right = new ButtonBuilder().setEmoji(emojis.right).setStyle(ButtonStyle.Primary).setCustomId('snake_right').setDisabled(true);
        const stop = new ButtonBuilder().setLabel(this.options.stopButton).setStyle(ButtonStyle.Danger).setCustomId('snake_stop').setDisabled(true);
        const dis1 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis1').setDisabled(true);
        const dis2 = new ButtonBuilder().setLabel('\u200b').setStyle(ButtonStyle.Secondary).setCustomId('dis2').setDisabled(true);
        const row1 = new ActionRowBuilder().addComponents(dis1, up, dis2, stop);
        const row2 = new ActionRowBuilder().addComponents(left, down, right);
        return interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    formatMessage(opts, key) {
        let content = opts[key];
        content = content.replace('{player}', `<@!${opts.message.author.id}>`);
        content = content.replace('{player.tag}', opts.message.author.tag);
        content = content.replace('{player.username}', opts.message.author.username);
        return content;
    }
}

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('snake')
        .setDescription('Play the classic Snake game'),

    async execute(interaction) {
        try {
            const game = new SnakeGame({
                message: interaction,
                isSlashGame: true,
                embed: { title: '🐍 Snake Game', color: config.EmbedConfig.embedColor, overTitle: '💀 Game Over' },
                emojis: { board: '⬛', food: '🍎', up: '⬆️', down: '⬇️', left: '⬅️', right: '➡️' },
                snake: { head: '💀', body: '💀', tail: '💀', skull: '💀' },
                stopButton: 'Stop Game',
                timeoutTime: 60000,
                playerOnlyMessage: 'Only {player} can use these buttons.'
            });
            activeSnakeGames.set(interaction.user.id, game);
            game.on('gameOver', () => activeSnakeGames.delete(interaction.user.id));
            await game.startGame();
        } catch (e) {
            console.error(e);
            if (!interaction.replied) await interaction.reply({ content: '❌ Error starting snake.', ephemeral: true });
        }
    },

    async handleButtonInteraction(interaction) {
        const game = activeSnakeGames.get(interaction.user.id);
        if (game) {
            return game.handleButtonClick(interaction);
        } else {
            return interaction.reply({ content: '❌ No active snake game found for you!', ephemeral: true });
        }
    }
};
