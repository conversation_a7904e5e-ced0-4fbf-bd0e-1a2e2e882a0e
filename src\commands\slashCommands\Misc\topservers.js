const { Slash<PERSON><PERSON>mandB<PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, AttachmentBuilder } = require('discord.js');
const { createCanvas, registerFont } = require('canvas');
const fs = require('fs');
const config = require('../../../../config/config.js');
const path = require('path');

const cmdname = path.parse(__filename).name; // file name
const folderName = path.basename(__dirname); // folder name
// Optional: Register custom fonts if you have them
// registerFont(path.join(__dirname, 'fonts/arial.ttf'), { family: 'Arial' });

// Helper function to normalize styled Unicode text to plain text
function normalizeText(text) {
    if (!text) return '';

    const customMapping = {
        'Ξ': 'E',
        'χ': 'x'
    };

    let processedText = '';
    for (const char of text) {
        processedText += customMapping[char] || char;
    }

    // Use NFKD normalization to decompose styled characters into their base form.
    // e.g., '𝐓' becomes 'T', 'é' becomes 'e' + '´'
    const normalized = processedText.normalize('NFKD');

    // After normalization, remove characters that are not letters (from any language),
    // numbers, or common punctuation. This allows for a broader range of characters.
   return normalized.replace(/[^\p{L}\p{N}\s.,!?'"()\[\]{}<>@#$%^&*\-_=+|\\/:;`~]/gu, '');
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('topservers')
        .setDescription('Shows the top 10 servers using our bot.')
        .addStringOption(option =>
            option.setName('theme')
                .setDescription('Choose a theme for the leaderboard')
                .setRequired(false)
                .addChoices(
                    { name: 'Default', value: 'default' },
                    { name: 'Solo Leveling', value: 'arise' }
                )),
    name: cmdname,
    category: folderName,
    aliases: ['top-servers', 'servers', 'leaderboard'],
    cooldown: 60, 
    usage: '',
    description: 'Shows the top 10 servers using our bot.',
    memberpermissions: [], 
    botpermissions: [],
    requiredroles: [], 
    requiredchannels: [], 
    alloweduserids: [], 
    minargs: 0,
    maxargs: 0,
    nsfw: false, 
    OwnerOnly: false, 
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    BetaServer: true,
    
    async execute(interaction) {
        try {
            await interaction.deferReply();

            const client = interaction.client;
            const guilds = client.guilds.cache;
            const theme = interaction.options.getString('theme') || 'default';

            // Get all guilds and sort by member count
            const sortedGuilds = guilds
                .map(guild => ({
                    name: guild.name,
                    memberCount: guild.memberCount,
                    id: guild.id,
                    icon: guild.iconURL({ dynamic: true, size: 64 }) || null,
                    owner: guild.ownerId,
                    createdAt: guild.createdAt
                }))
                .sort((a, b) => b.memberCount - a.memberCount)
                .slice(0, 10);

            if (sortedGuilds.length === 0) {
                const noServersEmbed = new EmbedBuilder()
                    .setTitle('📊 Top Servers')
                    .setDescription('No servers found.')
                    .setColor(config.EmbedConfig.embedcolor)
                    .setTimestamp();

                return await interaction.editReply({ embeds: [noServersEmbed] });
            }

            // Calculate statistics
            const totalMembers = sortedGuilds.reduce((acc, guild) => acc + guild.memberCount, 0);
            const allGuildsTotal = guilds.reduce((acc, guild) => acc + guild.memberCount, 0);

            // Generate the leaderboard image using Canvas based on theme
            let imageBuffer;
            if (theme === 'arise') {
                imageBuffer = await generateAriseLeaderboardCanvas(sortedGuilds, {
                    totalServers: guilds.size,
                    totalMembers: allGuildsTotal,
                    avgMembers: Math.round(totalMembers / 10),
                    botName: client.user.username,
                    botAvatar: client.user.displayAvatarURL({ format: 'png', size: 128 })
                });
            } else {
                imageBuffer = await generateDefaultLeaderboardCanvas(sortedGuilds, {
                    totalServers: guilds.size,
                    totalMembers: allGuildsTotal,
                    avgMembers: Math.round(totalMembers / 10),
                    botName: client.user.username,
                    botAvatar: client.user.displayAvatarURL({ format: 'png', size: 128 })
                });
            }

            // Create attachment
            const attachment = new AttachmentBuilder(imageBuffer, { name: 'leaderboard.png' });

            // Simple embed to accompany the image
            const embed = new EmbedBuilder()
                .setColor(theme === 'arise' ? '#ff6b35' : '#3b82f6')
                .setImage('attachment://leaderboard.png')
                .setTimestamp();

            await interaction.editReply({ files: [attachment] });

        } catch (error) {
            console.error('Error in topservers command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Something went wrong')
                .setDescription('Failed to generate leaderboard. Please try again in a moment!')
                .setColor('#ef4444')
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },
};

// Particle system for Solo Leveling atmosphere
class ParticleSystem {
    constructor(canvas) {
        this.canvas = canvas;
        this.particles = [];
        this.initParticles();
    }

    initParticles() {
        // Create different types of particles
        const particleCount = 80;
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 3 + 1,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                opacity: Math.random() * 0.7 + 0.3,
                type: Math.random() > 0.7 ? 'glow' : 'normal',
                color: this.getParticleColor(),
                pulsePhase: Math.random() * Math.PI * 2,
                life: 1.0
            });
        }

        // Add some special "mana" particles
        for (let i = 0; i < 15; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 6 + 3,
                speedX: (Math.random() - 0.5) * 0.3,
                speedY: (Math.random() - 0.5) * 0.3,
                opacity: Math.random() * 0.5 + 0.2,
                type: 'mana',
                color: Math.random() > 0.5 ? '#8b5cf6' : '#06b6d4',
                pulsePhase: Math.random() * Math.PI * 2,
                life: 1.0
            });
        }
    }

    getParticleColor() {
        const colors = ['#60a5fa', '#8b5cf6', '#06b6d4', '#3b82f6', '#6366f1', '#a855f7'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    draw(ctx) {
        this.particles.forEach(particle => {
            ctx.save();
            
            // Calculate pulsing effect
            const pulse = Math.sin(Date.now() * 0.005 + particle.pulsePhase) * 0.3 + 0.7;
            const currentOpacity = particle.opacity * pulse * particle.life;
            
            if (particle.type === 'mana') {
                // Draw mana particles with glow effect
                ctx.shadowColor = particle.color;
                ctx.shadowBlur = particle.size * 3;
                ctx.globalAlpha = currentOpacity * 0.6;
                ctx.fillStyle = particle.color;
                
                // Draw outer glow
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size * 1.5, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw inner core
                ctx.shadowBlur = particle.size;
                ctx.globalAlpha = currentOpacity;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);
                ctx.fill();
                
            } else if (particle.type === 'glow') {
                // Glowing particles
                ctx.shadowColor = particle.color;
                ctx.shadowBlur = particle.size * 2;
                ctx.globalAlpha = currentOpacity;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                
            } else {
                // Normal particles
                ctx.globalAlpha = currentOpacity * 0.8;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            }
            
            ctx.restore();
        });
    }
}

async function generateAriseLeaderboardCanvas(serverData, stats) {
    try {
        // Create canvas with exact dimensions
        const canvas = createCanvas(1200, 750);
        const ctx = canvas.getContext('2d');

        // Initialize particle system
        const particles = new ParticleSystem(canvas);

        // Helper function for number formatting
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }

        // Helper function for rounded rectangles
        function drawRoundedRect(x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }

        // Enhanced gradient background - Solo Leveling inspired
        const gradient = ctx.createRadialGradient(600, 375, 0, 600, 375, 800);
        gradient.addColorStop(0, '#0f0f23');
        gradient.addColorStop(0.3, '#1a1a3a');
        gradient.addColorStop(0.6, '#2d1b69');
        gradient.addColorStop(1, '#0f172a');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add subtle overlay pattern
        const overlayGradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        overlayGradient.addColorStop(0, 'rgba(99, 102, 241, 0.1)');
        overlayGradient.addColorStop(0.5, 'rgba(139, 92, 246, 0.05)');
        overlayGradient.addColorStop(1, 'rgba(6, 182, 212, 0.1)');
        ctx.fillStyle = overlayGradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw particles first (background layer)
        particles.draw(ctx);

        // Enhanced title with multiple layers and effects
        ctx.save();
        
        // Title shadow/outline effect
        ctx.strokeStyle = '#1e1b4b';
        ctx.lineWidth = 8;
        ctx.font = 'bold 68px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.strokeText('GUILD RANKINGS', 600, 75);
        
        // Title main gradient
        const titleGradient = ctx.createLinearGradient(200, 40, 1000, 80);
        titleGradient.addColorStop(0, '#f8fafc');
        titleGradient.addColorStop(0.2, '#e2e8f0');
        titleGradient.addColorStop(0.4, '#8b5cf6');
        titleGradient.addColorStop(0.6, '#6366f1');
        titleGradient.addColorStop(0.8, '#06b6d4');
        titleGradient.addColorStop(1, '#f8fafc');
        
        ctx.shadowColor = '#8b5cf6';
        ctx.shadowBlur = 25;
        ctx.shadowOffsetY = 3;
        ctx.fillStyle = titleGradient;
        ctx.fillText('GUILD RANKINGS', 600, 75);
        ctx.restore();

        // Enhanced subtitle with glow
        ctx.save();
        ctx.shadowColor = '#06b6d4';
        ctx.shadowBlur = 15;
        ctx.fillStyle = '#cbd5e1';
        ctx.font = 'bold 26px Arial, sans-serif';
        ctx.textAlign = 'center';
        const subtitleText = `${stats.botName.toUpperCase()} NETWORK`;
        ctx.fillText(subtitleText, 600, 110);
        
        // Add decorative elements
        ctx.fillStyle = '#8b5cf6';
        ctx.font = '20px Arial, sans-serif';
        ctx.fillText('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 600, 135);
        ctx.restore();

        // Draw server cards with enhanced styling
        const startX = 50;
        const startY = 180;
        const cardWidth = 350;
        const cardHeight = 95;
        const gapX = 25;
        const gapY = 12;
        const maxMembers = serverData[0].memberCount;

        for (let i = 0; i < serverData.length; i++) {
            const server = serverData[i];
            const rank = i + 1;
            const isTop3 = rank <= 3;
            const percentage = (server.memberCount / maxMembers) * 100;
            
            const col = i % 2;
            const row = Math.floor(i / 2);
            const x = startX + col * (cardWidth + gapX);
            const y = startY + row * (cardHeight + gapY);

            ctx.save();

            // Enhanced card background with multiple layers
            if (isTop3) {
                // Outer glow for top 3
                ctx.shadowColor = rank === 1 ? '#fbbf24' : rank === 2 ? '#e5e7eb' : '#f97316';
                ctx.shadowBlur = 20;
                ctx.fillStyle = 'rgba(139, 92, 246, 0.3)';
                drawRoundedRect(x - 2, y - 2, cardWidth + 4, cardHeight + 4, 14);
                ctx.fill();
                
                // Main card
                ctx.shadowBlur = 10;
                ctx.fillStyle = 'rgba(30, 27, 75, 0.9)';
                ctx.strokeStyle = 'rgba(139, 92, 246, 0.8)';
                ctx.lineWidth = 2;
            } else {
                ctx.fillStyle = 'rgba(15, 23, 42, 0.8)';
                ctx.strokeStyle = 'rgba(100, 116, 139, 0.4)';
                ctx.lineWidth = 1;
            }

            drawRoundedRect(x, y, cardWidth, cardHeight, 12);
            ctx.fill();
            ctx.stroke();
            ctx.restore();

            // Enhanced rank display
            let rankDisplay, rankColor, rankBg;
            if (rank === 1) {
                rankDisplay = '👑';
                rankColor = '#fbbf24';
                rankBg = 'rgba(251, 191, 36, 0.2)';
            } else if (rank === 2) {
                rankDisplay = '🥈';
                rankColor = '#e5e7eb';
                rankBg = 'rgba(229, 231, 235, 0.2)';
            } else if (rank === 3) {
                rankDisplay = '🥉';
                rankColor = '#f97316';
                rankBg = 'rgba(249, 115, 22, 0.2)';
            } else {
                rankDisplay = `#${rank}`;
                rankColor = '#64748b';
                rankBg = 'rgba(100, 116, 139, 0.2)';
            }

            // Rank background circle
            ctx.save();
            ctx.fillStyle = rankBg;
            ctx.beginPath();
            ctx.arc(x + 40, y + 47, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();

            // Rank text/emoji
            ctx.save();
            ctx.shadowColor = rankColor;
            ctx.shadowBlur = 12;
            ctx.fillStyle = rankColor;
            ctx.font = rank <= 3 ? '32px Arial, sans-serif' : 'bold 28px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(rankDisplay, x + 40, y + 55);
            ctx.restore();

            // Enhanced server name with better styling
            const normalizedServerName = normalizeText(server.name);
            const serverName = normalizedServerName.length > 22 ? normalizedServerName.substring(0, 19) + '...' : normalizedServerName;
            
            ctx.save();
            ctx.shadowColor = 'rgba(248, 250, 252, 0.5)';
            ctx.shadowBlur = 8;
            ctx.fillStyle = '#f8fafc';
            ctx.font = 'bold 24px Arial, sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(serverName, x + 75, y + 35);
            ctx.restore();

            // Enhanced member count with icon
            ctx.save();
            ctx.fillStyle = '#94a3b8';
            ctx.font = '18px Arial, sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText('👥', x + 75, y + 58);
            
            ctx.fillStyle = '#cbd5e1';
            ctx.font = 'bold 18px Arial, sans-serif';
            ctx.fillText(server.memberCount.toLocaleString() + ' members', x + 100, y + 58);
            ctx.restore();

            // Enhanced progress bar with glow
            const progressX = x + cardWidth - 160;
            const progressY = y + cardHeight - 25;
            const progressWidth = 140;
            const progressHeight = 8;

            // Progress bar background
            ctx.save();
            ctx.fillStyle = 'rgba(15, 23, 42, 0.8)';
            drawRoundedRect(progressX, progressY, progressWidth, progressHeight, 4);
            ctx.fill();

            // Progress bar fill with enhanced gradient and glow
            const progressGradient = ctx.createLinearGradient(progressX, progressY, progressX + progressWidth, progressY);
            if (isTop3) {
                progressGradient.addColorStop(0, '#8b5cf6');
                progressGradient.addColorStop(0.5, '#6366f1');
                progressGradient.addColorStop(1, '#06b6d4');
            } else {
                progressGradient.addColorStop(0, '#475569');
                progressGradient.addColorStop(1, '#64748b');
            }

            ctx.shadowColor = isTop3 ? '#8b5cf6' : 'transparent';
            ctx.shadowBlur = isTop3 ? 8 : 0;
            ctx.fillStyle = progressGradient;
            const fillWidth = Math.max(2, (progressWidth * percentage) / 100);
            drawRoundedRect(progressX, progressY, fillWidth, progressHeight, 4);
            ctx.fill();
            ctx.restore();

            // Power level indicator (Solo Leveling style)
            if (isTop3) {
                ctx.save();
                ctx.fillStyle = rankColor;
                ctx.font = 'bold 12px Arial, sans-serif';
                ctx.textAlign = 'right';
                const powerLevel = Math.floor(percentage);
                ctx.fillText(`PWR: ${powerLevel}%`, x + cardWidth - 10, y + 78);
                ctx.restore();
            }
        }

        // Enhanced stats sidebar with Solo Leveling styling
        const statsX = 850;
        const statsY = 180;
        const statsWidth = 300;
        const statsHeight = 120;
        const statsGap = 25;

        const statData = [
            { value: stats.totalServers.toLocaleString(), label: 'TOTAL GUILDS', icon: '🏰' },
            { value: formatNumber(stats.totalMembers), label: 'TOTAL HUNTERS', icon: '⚔️' },
            { value: formatNumber(stats.avgMembers), label: 'AVG STRENGTH', icon: '💎' }
        ];

        statData.forEach((stat, index) => {
            const statY = statsY + index * (statsHeight + statsGap);
            
            ctx.save();
            
            // Stat card background with glow
            ctx.shadowColor = 'rgba(139, 92, 246, 0.3)';
            ctx.shadowBlur = 15;
            ctx.fillStyle = 'rgba(30, 27, 75, 0.9)';
            ctx.strokeStyle = 'rgba(139, 92, 246, 0.6)';
            ctx.lineWidth = 2;
            drawRoundedRect(statsX, statY, statsWidth, statsHeight, 12);
            ctx.fill();
            ctx.stroke();

            // Stat icon
            ctx.shadowBlur = 0;
            ctx.font = '28px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(stat.icon, statsX + 40, statY + 45);

            // Stat value with enhanced glow
            ctx.shadowColor = '#06b6d4';
            ctx.shadowBlur = 15;
            ctx.fillStyle = '#06b6d4';
            ctx.font = 'bold 32px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(stat.value, statsX + statsWidth/2 + 20, statY + 50);

            // Stat label
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.fillStyle = '#cbd5e1';
            ctx.font = 'bold 16px Arial, sans-serif';
            ctx.fillText(stat.label, statsX + statsWidth/2, statY + 75);
            
            // Decorative line
            ctx.strokeStyle = 'rgba(139, 92, 246, 0.5)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(statsX + 20, statY + 85);
            ctx.lineTo(statsX + statsWidth - 20, statY + 85);
            ctx.stroke();
            
            ctx.restore();
        });

        // Add footer text with Solo Leveling style
        ctx.save();
        ctx.fillStyle = 'rgba(148, 163, 184, 0.8)';
        ctx.font = 'bold 16px Arial, sans-serif';
        ctx.textAlign = 'center';
       // ctx.fillText('「 THE STRONGEST RISE TO THE TOP 」', 600, 720);
        ctx.restore();

        // Return the canvas buffer
        return canvas.toBuffer('image/png');

    } catch (error) {
        console.error('Error generating canvas leaderboard:', error);
        throw error;
    }
}

async function generateDefaultLeaderboardCanvas(serverData, stats) {
    try {
        // Create canvas with exact dimensions
        const canvas = createCanvas(1200, 750);
        const ctx = canvas.getContext('2d');

        // Helper function for number formatting
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toLocaleString();
        }

        // Helper function for rounded rectangles
        function drawRoundedRect(x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }

        // Default theme - Blue gradient background
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#050b14');
        gradient.addColorStop(0.5, '#0f1f3d');
        gradient.addColorStop(1, '#1e3a8a');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add blue overlay pattern
        const overlayGradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        overlayGradient.addColorStop(0, 'rgba(59, 130, 246, 0.1)');
        overlayGradient.addColorStop(0.5, 'rgba(96, 165, 250, 0.05)');
        overlayGradient.addColorStop(1, 'rgba(6, 182, 212, 0.1)');
        ctx.fillStyle = overlayGradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Enhanced title with blue theme
        ctx.save();
        
        // Title shadow/outline effect
        ctx.strokeStyle = '#1e3a8a';
        ctx.lineWidth = 8;
        ctx.font = 'bold 68px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.strokeText('SERVER LEADERBOARD', 600, 75);
        
        // Title main gradient - Blue theme
        const titleGradient = ctx.createLinearGradient(200, 40, 1000, 80);
        titleGradient.addColorStop(0, '#f8fafc');
        titleGradient.addColorStop(0.2, '#dbeafe');
        titleGradient.addColorStop(0.4, '#60a5fa');
        titleGradient.addColorStop(0.6, '#3b82f6');
        titleGradient.addColorStop(0.8, '#1d4ed8');
        titleGradient.addColorStop(1, '#00d4ff');
        
        ctx.shadowColor = '#60a5fa';
        ctx.shadowBlur = 25;
        ctx.shadowOffsetY = 3;
        ctx.fillStyle = titleGradient;
        ctx.fillText('SERVER LEADERBOARD', 600, 75);
        ctx.restore();

        // Enhanced subtitle with glow
        ctx.save();
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 15;
        ctx.fillStyle = '#dbeafe';
        ctx.font = 'bold 26px Arial, sans-serif';
        ctx.textAlign = 'center';
        const subtitleText = `${stats.botName.toUpperCase()} NETWORK`;
        ctx.fillText(subtitleText, 600, 110);
        
        // Add decorative elements
        ctx.fillStyle = '#3b82f6';
        ctx.font = '20px Arial, sans-serif';
        ctx.fillText('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 600, 135);
        ctx.restore();

        // Draw server cards with blue theme styling
        const startX = 50;
        const startY = 180;
        const cardWidth = 345;
        const cardHeight = 90;
        const gapX = 30;
        const gapY = 15;
        const maxMembers = serverData[0].memberCount;

        for (let i = 0; i < serverData.length; i++) {
            const server = serverData[i];
            const rank = i + 1;
            const isTop3 = rank <= 3;
            const percentage = (server.memberCount / maxMembers) * 100;
            
            const col = i % 2;
            const row = Math.floor(i / 2);
            const x = startX + col * (cardWidth + gapX);
            const y = startY + row * (cardHeight + gapY);

            ctx.save();

            // Card background with blue theme
            if (isTop3) {
                ctx.shadowColor = rank === 1 ? '#fbbf24' : rank === 2 ? '#e5e7eb' : '#f97316';
                ctx.shadowBlur = 20;
                ctx.fillStyle = 'rgba(59, 130, 246, 0.25)';
                ctx.strokeStyle = 'rgba(96, 165, 250, 0.6)';
                ctx.lineWidth = 2;
            } else {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 1;
            }

            drawRoundedRect(x, y, cardWidth, cardHeight, 12);
            ctx.fill();
            ctx.stroke();
            ctx.restore();

            // Rank display
            let rankDisplay, rankColor;
            if (rank === 1) {
                rankDisplay = '🥇';
                rankColor = '#fbbf24';
            } else if (rank === 2) {
                rankDisplay = '🥈';
                rankColor = '#e5e7eb';
            } else if (rank === 3) {
                rankDisplay = '🥉';
                rankColor = '#f97316';
            } else {
                rankDisplay = rank.toString();
                rankColor = '#60a5fa';
            }

            ctx.save();
            ctx.shadowColor = rankColor;
            ctx.shadowBlur = 10;
            ctx.fillStyle = rankColor;
            ctx.font = rank <= 3 ? '36px Arial, sans-serif' : 'bold 36px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(rankDisplay, x + 35, y + 50);
            ctx.restore();

            // Server name
            const normalizedServerName = normalizeText(server.name);
            const serverName = normalizedServerName.length > 24 ? normalizedServerName.substring(0, 21) + '...' : normalizedServerName;
            
            ctx.save();
            ctx.shadowColor = 'rgba(255, 255, 255, 0.3)';
            ctx.shadowBlur = 5;
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 26px Arial, sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(serverName, x + 70, y + 35);
            ctx.restore();

            // Member count
            ctx.fillStyle = '#dbeafe';
            ctx.font = '20px Arial, sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText('👥 ' + server.memberCount.toLocaleString(), x + 70, y + 60);

            // Progress bar with blue theme
            const progressX = x + cardWidth - 180;
            const progressY = y + cardHeight - 28;
            const progressWidth = 160;
            const progressHeight = 10;

            // Progress bar background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            drawRoundedRect(progressX, progressY, progressWidth, progressHeight, 5);
            ctx.fill();

            // Progress bar fill with blue gradient
            const progressGradient = ctx.createLinearGradient(progressX, progressY, progressX + progressWidth, progressY);
            progressGradient.addColorStop(0, '#3b82f6');
            progressGradient.addColorStop(0.5, '#60a5fa');
            progressGradient.addColorStop(1, '#00d4ff');

            ctx.save();
            ctx.shadowColor = 'rgba(59, 130, 246, 0.5)';
            ctx.shadowBlur = 10;
            ctx.fillStyle = progressGradient;
            const fillWidth = (progressWidth * percentage) / 100;
            drawRoundedRect(progressX, progressY, fillWidth, progressHeight, 5);
            ctx.fill();
            ctx.restore();
        }

        // Stats sidebar with blue theme
        const statsX = 850;
        const statsY = 180;
        const statsWidth = 300;
        const statsHeight = 130;
        const statsGap = 20;

        const statData = [
            { value: stats.totalServers.toLocaleString(), label: 'Total Servers' },
            { value: formatNumber(stats.totalMembers), label: 'Total Members' },
            { value: formatNumber(stats.avgMembers), label: 'Avg Members' }
        ];

        statData.forEach((stat, index) => {
            const statY = statsY + index * (statsHeight + statsGap);
            
            ctx.save();
            
            // Stat card background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.strokeStyle = 'rgba(96, 165, 250, 0.3)';
            ctx.lineWidth = 1;
            drawRoundedRect(statsX, statY, statsWidth, statsHeight, 12);
            ctx.fill();
            ctx.stroke();

            // Stat value with glow
            ctx.shadowColor = 'rgba(96, 165, 250, 0.8)';
            ctx.shadowBlur = 10;
            ctx.fillStyle = '#60a5fa';
            ctx.font = 'bold 36px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(stat.value, statsX + statsWidth/2, statY + statsHeight/2 - 5);

            // Reset shadow for label
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.fillStyle = '#dbeafe';
            ctx.font = '22px Arial, sans-serif';
            ctx.fillText(stat.label, statsX + statsWidth/2, statY + statsHeight/2 + 30);
            ctx.restore();
        });

        // Return the canvas buffer
        return canvas.toBuffer('image/png');

    } catch (error) {
        console.error('Error generating Arise canvas leaderboard:', error);
        throw error;
    }
}