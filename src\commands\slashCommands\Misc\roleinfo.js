const { SlashCommandBuilder } = require('@discordjs/builders');
const { AttachmentBuilder, EmbedBuilder } = require('discord.js');
const Canvas = require('canvas');
const path = require('path');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('rolesinfo')
    .setDescription('Displays the server roles information as a beautiful image'),
  
    name: 'rolesinfo',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: 'rolesinfo',
    description: 'Displays the server roles information as a beautiful image',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,

    async execute(interaction) {

      await interaction.deferReply();
    
    try {
      // Create all the role category images
      const leadershipImage = await createLeadershipImage();
      const specialRolesImage = await createSpecialRolesImage();
      const levelRanksImage = await createLevelRanksImage();
      const pingRolesImage = await createPingRolesImage();
      
      // Create attachments
      const leadershipAttachment = new AttachmentBuilder(leadershipImage, { name: 'leadership-roles.png' });
      const specialRolesAttachment = new AttachmentBuilder(specialRolesImage, { name: 'special-roles.png' });
      const levelRanksAttachment = new AttachmentBuilder(levelRanksImage, { name: 'level-ranks.png' });
      const pingRolesAttachment = new AttachmentBuilder(pingRolesImage, { name: 'ping-roles.png' });
      
      // Create embeds
      const leadershipEmbed = new EmbedBuilder()
        .setColor('#5d3fd3')  
        .setTitle('👑 LEADERSHIP HIERARCHY')
        .setImage('attachment://leadership-roles.png');
      
      const specialRolesEmbed = new EmbedBuilder()
        .setColor('#eb459e')
        .setTitle('🏅 SPECIAL ROLES')
        .setImage('attachment://special-roles.png');
      
      const levelRanksEmbed = new EmbedBuilder()
        .setColor('#ff0000') 
        .setTitle('⚖️ LEVEL RANKS')
        .setImage('attachment://level-ranks.png');
      
      const pingRolesEmbed = new EmbedBuilder()
        .setColor('#faa81a')
        .setTitle('📢 PING ROLES')
        .setImage('attachment://ping-roles.png')
        //.setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });
      
      // Main embed
      const mainEmbed = new EmbedBuilder()
        .setColor('#5865f2')
        .setDescription('# ROLES INFO - RankBreaker\n\n*"Stay sharp, Hunter! Progress through the ranks, and make your name legendary in this shadowed world."*');
      
      // Send the response with all embeds
      await interaction.editReply({ content: "Generating role information..." });
      await interaction.channel.send({
        embeds: [mainEmbed, leadershipEmbed, specialRolesEmbed, levelRanksEmbed, pingRolesEmbed], 
        files: [leadershipAttachment, specialRolesAttachment, levelRanksAttachment, pingRolesAttachment] 
      }); 
    } catch (error) {
      console.error('Error creating roles images:', error);
      await interaction.editReply('There was an error generating the server roles images.');
    }
  }
};

async function createLeadershipImage() {
  const canvas = Canvas.createCanvas(800, 500);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#18191c';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add border
  ctx.strokeStyle = '#5d3fd3';
  ctx.lineWidth = 5;
  ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);
  
  // Leadership roles
  const roles = [
    { emoji: '👑', role: 'Shadow Monarch', rank: 'Owner', desc: 'The supreme ruler of the server — commands all, shadows and mortals alike.' },
    { emoji: '⚔️', role: 'Vice Monarch', rank: 'Co-Owner', desc: 'The right hand of the Monarch. Helps rule and maintain order in the kingdom.' },
    { emoji: '💠', role: 'Rulers\' Envoy', rank: 'Admins', desc: 'Trusted representatives of the Monarch — uphold order, enforce rules, and oversee the shadows.' },
    { emoji: '🛡️', role: 'Shadow Enforcer', rank: 'Moderators', desc: 'The hidden blades of the server, ready to strike against chaos and maintain balance.' },
    { emoji: '🗡️', role: 'Guild Master', rank: 'Helper/Support Team', desc: 'Leaders of wisdom and support, guiding adventurers through their journey in the server.' }
  ];
  
  // Draw roles
  let y = 60;
  
  for (const role of roles) {
    // Role name with emoji
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'left';
    ctx.fillText(`${role.emoji} ${role.role}`, 50, y);
    
    // Rank
    ctx.font = 'italic 22px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#72767d';
    ctx.fillText(`(${role.rank})`, 400, y);
    
    y += 30;
    
    // Description
    ctx.font = '20px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#b9bbbe';
    
    // Word wrap for description
    const words = role.desc.split(' ');
    let line = '';
    let lineY = y;
    
    for (const word of words) {
      const testLine = line + word + ' ';
      const metrics = ctx.measureText(testLine);
      
      if (metrics.width > canvas.width - 100) {
        ctx.fillText(line, 70, lineY);
        line = word + ' ';
        lineY += 25;
      } else {
        line = testLine;
      }
    }
    
    ctx.fillText(line, 70, lineY);
    
    y = lineY + 40;
  }

  // Add watermark at the bottom
ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
ctx.fillStyle = 'rgba(93, 63, 211, 0.7)'; // Semi-transparent purple to match the border
ctx.textAlign = 'center';
ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
  
  return canvas.toBuffer();
}

async function createSpecialRolesImage() {
  const canvas = Canvas.createCanvas(800, 250);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#18191c';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add border
  ctx.strokeStyle = '#eb459e';
  ctx.lineWidth = 5;
  ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);
  
  // Special roles
  const roles = [
    { emoji: '😎', role: 'Monarch\'s Shadow', rank: 'Boosters', desc: 'Honored patrons who support the realm. True benefactors of the Monarch\'s empire.' },
    { emoji: '🔥', role: 'Gate Breaker', rank: 'Event Winners', desc: 'Legends who\'ve proven their might by winning events and breaking through the gates!' }
  ];
  
  // Draw roles
  let y = 60;
  
  for (const role of roles) {
    // Role name with emoji
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'left';
    ctx.fillText(`${role.emoji} ${role.role}`, 50, y);
    
    // Rank
    ctx.font = 'italic 22px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#72767d';
    ctx.fillText(`(${role.rank})`, 400, y);
    
    y += 30;
    
    // Description
    ctx.font = '20px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#b9bbbe';
    
    // Word wrap for description
    const words = role.desc.split(' ');
    let line = '';
    let lineY = y;
    
    for (const word of words) {
      const testLine = line + word + ' ';
      const metrics = ctx.measureText(testLine);
      
      if (metrics.width > canvas.width - 100) {
        ctx.fillText(line, 70, lineY);
        line = word + ' ';
        lineY += 25;
      } else {
        line = testLine;
      }
    }
    
    ctx.fillText(line, 70, lineY);
    
    y = lineY + 40;
  }

  // Add watermark at the bottom
ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
ctx.fillStyle = 'rgba(235, 69, 158, 0.7)'; // Semi-transparent pink to match the border
ctx.textAlign = 'center';
ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
  
  return canvas.toBuffer();
}

async function createLevelRanksImage() {
  const canvas = Canvas.createCanvas(875, 650);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#18191c';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add border
  ctx.strokeStyle = '#ff0000'; // Red color 
  ctx.lineWidth = 5;
  ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);
  
  // Level ranks
  const roles = [
    { emoji: '🌑', role: 'N+ Rank', rank: 'Level 100', desc: 'Transcendent beings with unmatched power, beyond the limits of the world.' },
    { emoji: '🌌', role: 'N Rank', rank: 'Level 90', desc: 'Beings of near-godlike strength, feared by all.' },
    { emoji: '💎', role: 'G Rank', rank: 'Level 80', desc: 'The rarest guild elites, few have ever seen them.' },
    { emoji: '⚡', role: 'SS Rank', rank: 'Level 70', desc: 'Master hunters who dominate dungeons and battles alike.' },
    { emoji: '🔥', role: 'S Rank', rank: 'Level 60', desc: 'Elite hunters, renowned for legendary strength.' },
    { emoji: '💠', role: 'A Rank', rank: 'Level 50', desc: 'Advanced warriors, trusted and respected by all.' },
    { emoji: '🟣', role: 'B Rank', rank: 'Level 40', desc: 'Seasoned elites, feared and admired across the realm.' },
    { emoji: '🔵', role: 'C Rank', rank: 'Level 30', desc: 'Respected fighters, well-versed in the art of battle.' },
    { emoji: '🟢', role: 'D Rank', rank: 'Level 20', desc: 'Warriors who\'ve sharpened their skills and earned recognition.' },
    { emoji: '🟤', role: 'E Rank', rank: 'Level 10', desc: 'New adventurers who\'ve just awakened their powers.' }
  ];
  
  // Draw roles
  let y = 50;
  
  for (const role of roles) {
    // Role name with emoji
    ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'left';
    ctx.fillText(`${role.emoji} ${role.role}`, 50, y);
    
    // Rank
    ctx.font = 'italic 22px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#72767d';
    ctx.fillText(`(${role.rank})`, 350, y);
    
    y += 25;
    
    // Description
    ctx.font = '18px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#b9bbbe';
    ctx.fillText(role.desc, 70, y);
    
    y += 35;
  }

// Add watermark at the bottom
ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
ctx.fillStyle = 'rgba(255, 0, 0, 0.7)'; // Semi-transparent red to match the border
ctx.textAlign = 'center';
ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
  
  return canvas.toBuffer();
}

async function createPingRolesImage() {
  const canvas = Canvas.createCanvas(850, 630);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#18191c';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add border
  ctx.strokeStyle = '#faa81a'; 
  ctx.lineWidth = 5;
  ctx.strokeRect(8, 8, canvas.width - 16, canvas.height - 16);
  
  // Ping roles
  const roles = [
    { emoji: '🌀', role: 'Dungeons Ping', desc: 'General ping for dungeon-related announcements.' },
    { emoji: '✨', role: 'Double Dungeon Ping', desc: 'For double dungeon rewards and events.' },
    { emoji: '🚪', role: 'Red Gate Ping', desc: 'For Red Gate special alert missions.' },
    { emoji: '💎', role: 'SS Dungeon Ping', desc: 'For high-level SS Dungeon raids.' },
    { emoji: '🔥', role: 'S Dungeon Ping', desc: 'For S-tier dungeon openings.' },
    { emoji: '💠', role: 'A Dungeon Ping', desc: 'For A-tier dungeon hunts.' },
    { emoji: '🟣', role: 'B Dungeon Ping', desc: 'For B-tier dungeon explorations.' },
    { emoji: '🔵', role: 'C Dungeon Ping', desc: 'For C-tier dungeon missions.' },
    { emoji: '🟢', role: 'D Dungeon Ping', desc: 'For D-tier dungeon groups.' },
    { emoji: '🟤', role: 'E Dungeon Ping', desc: 'For E-tier dungeon starters.' }
  ];
  
  // Draw roles
  let y = 50;
  
  for (const role of roles) {
    // Role name with emoji
    ctx.font = 'bold 26px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'left';
    ctx.fillText(`${role.emoji} ${role.role}`, 50, y);
    
    y += 25;
    
    // Description
    ctx.font = '18px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#b9bbbe';
    ctx.fillText(role.desc, 70, y);
    
    y += 32;
  }

  ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
  ctx.fillStyle = 'rgba(250, 168, 26, 0.7)'; // Semi-transparent orange to match the border
  ctx.textAlign = 'center';
  ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
  
  return canvas.toBuffer();
}